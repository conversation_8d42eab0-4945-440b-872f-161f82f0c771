package com.soundrecorder.wavemark.mark

import android.net.Uri
import android.os.Build
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.CoroutineScope
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.base.PlayerHelperCallback
import com.soundrecorder.common.utils.MarkSerializUtil
import com.soundrecorder.wavemark.mark.MarkHelper.Companion.ADD_RESULT_LIMIT_NUMBER_EXCEED
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.mock
import org.mockito.Mockito.spy
import org.mockito.invocation.InvocationOnMock
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MarkHelperTest {

    companion object {
        private val BEAN_ZERO = MarkDataBean(0L).apply { defaultNo = 0 }
        private val BEAN_ONE = MarkDataBean(1000L).apply { defaultNo = 1 }
        private val BEAN_TWO = MarkDataBean(2000L).apply { defaultNo = 2 }
        private val OLD_BEAN_ONE = MarkDataBean(10000L, MarkSerializUtil.VERSION_OLD).apply { defaultNo = 5 }
        private val OLD_BEAN_TWO = MarkDataBean(20000L, MarkSerializUtil.VERSION_OLD).apply { defaultNo = 6 }
        private val BEAN_PICTURE = MarkDataBean(10000L, MarkSerializUtil.VERSION_PICTURE).apply { defaultNo = 7 }
    }

    private var callback: PlayerHelperCallback? = null
    private var viewModelScope: CoroutineScope? = null
    private var instance: MarkHelper? = null
    private var mTextUtilsMockedStatic: MockedStatic<TextUtils>? = null


    @Before
    fun setUp() {
        callback = mock(PlayerHelperCallback::class.java)
        viewModelScope = mock(CoroutineScope::class.java)
        instance = spy(
            MarkHelper(
                callback,
                viewModelScope!!,
                false
            )
        )
        mTextUtilsMockedStatic = Mockito.mockStatic(TextUtils::class.java)
        mTextUtilsMockedStatic?.`when`<Any> { TextUtils.isEmpty(ArgumentMatchers.any()) }?.thenAnswer { invocation: InvocationOnMock ->
            val a = invocation.getArgument<CharSequence>(0)
            if (a == null || a.length == 0) {
                return@thenAnswer true
            }
            false
        }
    }

    @After
    fun tearDown() {
        instance = null
        viewModelScope = null
        callback = null
        mTextUtilsMockedStatic?.close()
        mTextUtilsMockedStatic = null
    }

    @Test
    fun should_refresh_when_refreshData() {
        instance?.refreshData(null, null, null)
        assertNull(Whitebox.getInternalState(instance, "uri"))

        val markDataBeans = mutableListOf<MarkDataBean>(BEAN_ONE, BEAN_TWO, OLD_BEAN_TWO)
        val uri = mock(Uri::class.java)
        val pathString = "path/string/test"
        doReturn(15000L).`when`(callback)?.getDuration()
        instance?.refreshData(markDataBeans, uri, pathString)
//        PowerMockito.verifyPrivate(instance, times(1)).invoke("correctMarkData", markDataBeans)
        assertEquals(uri, Whitebox.getInternalState(instance, "uri"))
        assertEquals(pathString, Whitebox.getInternalState(instance, "pathString"))
    }

    @Test
    fun should_correct_when_isMarkEnabled_different_inputs() {
        val markDataBeans = mock(MutableList::class.java)
        val markDatas = mock(MutableLiveData::class.java)
        doReturn(markDataBeans).`when`(markDatas).value
        doReturn(50, 1).`when`(markDataBeans).size
        doReturn(BEAN_ONE).`when`(markDataBeans)[0]
        Whitebox.setInternalState(instance, "markDatas", markDatas)
        assertFalse(instance?.isMarkEnabled() ?: true)
        doReturn(true, false).`when`(callback)?.hasPaused()
        doReturn(1000L, 10000L).`when`(callback)?.getCurrentPlayerTime()
        doReturn(5000L, 20000L).`when`(callback)?.getDuration()
        assertFalse(instance?.isMarkEnabled() ?: true)
        assertFalse(instance?.isMarkEnabled() ?: true)
        assertTrue(instance?.isMarkEnabled() ?: false)
    }

    @Test
    fun should_correct_when_getLastMark() {
        Whitebox.setInternalState(instance, "lastMarkIndex", -1)
        var result = instance?.getLastMark()
        assertNull(result)
        val markDataBeans = mutableListOf(BEAN_ONE, BEAN_TWO)
        val markDatas = MutableLiveData(markDataBeans)
        Whitebox.setInternalState(instance, "markDatas", markDatas)
        Whitebox.setInternalState(instance, "lastMarkIndex", 0)
        result = instance?.getLastMark()
        assertEquals(BEAN_ONE, result)
        Whitebox.setInternalState(instance, "lastMarkIndex", 3)
        result = instance?.getLastMark()
        assertNull(result)
    }

    @Test
    fun should_correct_when_addMark_different_inputs() {
        instance?.playerHelperCallback = null
        val markMetaData = MarkMetaData("", "", 0, -1, -1)
        assertEquals(MarkHelper.ADD_RESULT_OTHER_ERRORS, instance?.addMark(false, markMetaData))

        instance?.playerHelperCallback = callback
        val markDataBeans = mock(MutableList::class.java)
        val markDatas = mock(MutableLiveData::class.java)
        doReturn(markDataBeans).`when`(markDatas).value
        doReturn(50).`when`(markDataBeans).size
        Whitebox.setInternalState(instance, "markDatas", markDatas)
        assertEquals(ADD_RESULT_LIMIT_NUMBER_EXCEED, instance?.addMark(false, MarkMetaData(currentTimeMillis = 1000L, imagePath = "path/test")))

        val markList = MutableLiveData(mutableListOf(MarkDataBean(2000)))
        Whitebox.setInternalState(instance, "markDatas", markList)
        assertEquals(MarkHelper.ADD_RESULT_DUPLICATE_TIME, instance?.addMark(true, MarkMetaData(currentTimeMillis = 1500L, imagePath = "path/test")))
        assertNotNull(instance?.getLastMark())

        doReturn(15000L).`when`(callback)?.getDuration()
        assertEquals(MarkHelper.ADD_RESULT_PLAY_COMPLETE, instance?.addMark(true, MarkMetaData(currentTimeMillis = 15000L, imagePath = "path/test")))

        doReturn("").`when`(callback)?.getKeyId()
        assertEquals(1, instance?.addMark(true, MarkMetaData(currentTimeMillis = 7000L, imagePath = "path/test")))
    }

    @Test
    fun should_correct_when_removeMark() {
        val markDataBeans = mutableListOf(BEAN_ONE, BEAN_TWO)
        val markDatas = MutableLiveData(markDataBeans)
        Whitebox.setInternalState(instance, "markDatas", markDatas)
        assertNull(instance?.removeMark(10))
        /*Mockito.doNothing().`when`(Whitebox.invokeMethod<Unit>(instance, "deletePictureMark", anyString(), any()))
        assertEquals(BEAN_ONE, instance?.removeMark(0))*/
    }

    @Test
    fun should_correct_when_renameMark() {
        val markDataBeans = mutableListOf(BEAN_ONE, BEAN_TWO)
        val markDatas = MutableLiveData(markDataBeans)
        Whitebox.setInternalState(instance, "markDatas", markDatas)
        assertFalse(instance?.renameMark("testRenameMark", 10) ?: true)
       /* val markDataBean = MarkDataBean(1000L).apply {
            isDefault = true
            markText = "testRenameMark"
        }
        Mockito.doNothing().`when`(Whitebox.invokeMethod<Unit>(instance, "updatePictureMark", "mediaId", markDataBean))
        doReturn("mediaId").`when`(callback)?.getKeyId()
        assertTrue(instance?.renameMark("testRenameMark", 0) ?: false)*/
    }

    @Test
    fun should_clear_when_clear() {
        val markDataBeans = mutableListOf(BEAN_ONE, BEAN_TWO)
        val markDatas = MutableLiveData(markDataBeans)
        Whitebox.setInternalState(instance, "markDatas", markDatas)
        Whitebox.setInternalState(instance, "uri", mock(Uri::class.java))
        Whitebox.setInternalState(instance, "pathString", "pathString")
        assertEquals(2, instance?.getMarkDatas()?.value?.size)
        assertNotNull(Whitebox.getInternalState(instance, "uri"))
        assertNotNull(Whitebox.getInternalState(instance, "pathString"))
        instance?.clear()
        assertEquals(0, instance?.getMarkDatas()?.value?.size)
        assertNull(Whitebox.getInternalState(instance, "uri"))
        assertNull(Whitebox.getInternalState(instance, "pathString"))
    }

    @Test
    fun should_when_startIndexWhenReadMarksComplete() {
        instance?.startIndexWhenReadMarksComplete(null)
        assertEquals(
            0,
            Whitebox.getInternalState<MarkerCounter>(instance, "markerCounter").startIndex
        )
        instance?.startIndexWhenReadMarksComplete(mutableListOf(BEAN_ONE))
        assertEquals(
            1,
            Whitebox.getInternalState<MarkerCounter>(instance, "markerCounter").startIndex
        )
    }
}