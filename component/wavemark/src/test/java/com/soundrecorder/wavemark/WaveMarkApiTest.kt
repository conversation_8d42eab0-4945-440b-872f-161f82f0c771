/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveMarkApiTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/1/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark

import android.net.Uri
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import com.soundrecorder.wavemark.wave.load.AmplitudeListUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class WaveMarkApiTest {

    private var mockedStaticAmplitudeListUtil: MockedStatic<AmplitudeListUtil>? = null

    @Before
    fun init() {
        mockedStaticAmplitudeListUtil = Mockito.mockStatic(AmplitudeListUtil::class.java)
    }

    @After
    fun release() {
        mockedStaticAmplitudeListUtil?.close()
        mockedStaticAmplitudeListUtil = null
    }

    @Test
    fun should_invoke_when_decodeAmplitudeByUri() {
        val expected = "123"
        Mockito.`when`(AmplitudeListUtil.decodeAmplitudeByUri(any(Uri::class.java)))
            .thenReturn(expected)
        val realValue = WaveMarkApi.decodeAmplitudeByUri(Uri.EMPTY)
        Assert.assertEquals(expected, realValue)
    }

    private fun <T> any(type: Class<T>): T = Mockito.any<T>(type)
}