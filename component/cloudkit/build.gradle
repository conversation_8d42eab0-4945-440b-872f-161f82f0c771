apply from: "../../common_flavor_build.gradle"

apply plugin: 'kotlin-kapt'

android {

    namespace "com.recorder.cloudkit"

    buildFeatures {
        buildConfig true
    }

    productFlavors {
        domestic {
            /*CLOUDKIT 内外销二级域名配置，内销： 测试环境：wanyol.com 预发布环境：oppomobile.com 正式环境：heytapmobi.com*/
            buildConfigField "String", "CLOUDKIT_HOST", "\"kfzwbsnlaj-`ln\"" // heytapmobi.com
            buildConfigField "String", "HOST_PRIVCY_URL", "\"\""
        }
        export {
            /*heytapmobile.com*/
            buildConfigField "String", "CLOUDKIT_HOST", "\"kfzwbsnlajof-`ln\""
            // 云服务隐私政策，仅在外销上使用
//            buildConfigField "String", "HOST_PRIVCY_URL", "\"https://static-cn01a-ocloud.heytapimage.com/heytapprivacystatement.html?new_open=true&BRAND-SHOW-TYPE=1&OCLOUD-LANG=en_US&OCLOUD-EXP=true\""
            buildConfigField "String", "HOST_PRIVCY_URL", "\"kwwsp9,,pwbwj`.`m32b.l`olvg-kfzwbsjnbdf-`ln,kfzwbssqjub`zpwbwfnfmw-kwno\""

            buildConfigField "String", "OFFLINE_NOTICE_URL", "\"kwwsp9,,`olvg-kfzwbs-`ln,pfquj`f`olpvqf-kwno\""
        }
        //apk名称需要重命名为：“GDPR”
        gdpr {
            buildConfigField "String", "CLOUDKIT_HOST", "\"\""
            buildConfigField "String", "HOST_PRIVCY_URL", "\"\""
        }
    }

    buildTypes {
        debug {
            /*PUSH-oppo录音： 测试服key：35194ab5d64e4920a6c931c21a9da965 测试服secret：31f5054cb64f4fadb581e110c6314798*/
            buildConfigField "String", "PUSH_APP_KEY_OPPO", "\"ead963094bc0406197ba1ad2fe6a26f2\""
            buildConfigField "String", "PUSH_APP_SECRET_OPPO", "\"d184c8315c244d34b774bcb126cc7d12\""
            /*PUSH 一加录音*/
            buildConfigField "String", "PUSH_APP_KEY_ONEPLUS", "\"90e2b641544c4804852888427f766010\""
            buildConfigField "String", "PUSH_APP_SECRET_ONEPLUS", "\"cb0db60a248041bba3116a482397ac90\""

            /*cloudkit相关id*/
            buildConfigField "String", "CLOUDKIT_APP_ID", "\"a4569aa137bbcafc\""
            buildConfigField "String", "CLOUDKIT_APP_PKG_ID_OPPO", "\"b1de8d239fb137a8\""
            buildConfigField "String", "CLOUDKIT_APP_PKG_ID_ONEPLUS", "\"51cfe5a516df50f2\""
            // 测试：e0a2135455b8410ba5634365507d5a42
            buildConfigField "String", "CLOUDKIT_APP_KEY_OPPO", "\"b87e5bfbec254357a608c54022ecfc36\""
            // 测试：af52694821b136adab5c7bf28b61958fb297f0617c48d5a0413609547de13040
            buildConfigField "String", "CLOUDKIT_APP_SECRET_OPPO", "\"9b1ea1d739f4873e8ed5ed1683a9789df2311500aa15173197be00690b0022b7\""

            buildConfigField "String", "CLOUDKIT_APP_KEY_ONEPLUS", "\"2eb981430e8d4a56ac6582ea916d6d8c\""
            buildConfigField "String", "CLOUDKIT_APP_SECRET_ONEPLUS", "\"51a235a3f0797fcdf050be40d6e92f261798aebaa543096e4f7ae2ed1b747530\""
        }
        release {
            /*PUSH oppo录音*/
            buildConfigField "String", "PUSH_APP_KEY_OPPO", "\"ead963094bc0406197ba1ad2fe6a26f2\""
            buildConfigField "String", "PUSH_APP_SECRET_OPPO", "\"d184c8315c244d34b774bcb126cc7d12\""
            /*PUSH 一加录音*/
            buildConfigField "String", "PUSH_APP_KEY_ONEPLUS", "\"90e2b641544c4804852888427f766010\""
            buildConfigField "String", "PUSH_APP_SECRET_ONEPLUS", "\"cb0db60a248041bba3116a482397ac90\""
            /*cloukit相关id*/
            buildConfigField "String", "CLOUDKIT_APP_ID", "\"a4569aa137bbcafc\""
            buildConfigField "String", "CLOUDKIT_APP_PKG_ID_OPPO", "\"b1de8d239fb137a8\""
            buildConfigField "String", "CLOUDKIT_APP_PKG_ID_ONEPLUS", "\"51cfe5a516df50f2\""
            // 测试：e0a2135455b8410ba5634365507d5a42
            buildConfigField "String", "CLOUDKIT_APP_KEY_OPPO", "\"b87e5bfbec254357a608c54022ecfc36\""
            buildConfigField "String", "CLOUDKIT_APP_SECRET_OPPO", "\"9b1ea1d739f4873e8ed5ed1683a9789df2311500aa15173197be00690b0022b7\""

            buildConfigField "String", "CLOUDKIT_APP_KEY_ONEPLUS", "\"2eb981430e8d4a56ac6582ea916d6d8c\""
            buildConfigField "String", "CLOUDKIT_APP_SECRET_ONEPLUS", "\"51a235a3f0797fcdf050be40d6e92f261798aebaa543096e4f7ae2ed1b747530\""
        }
    }
}

dependencies {
    implementation libs.androidx.support

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.androidx.lifecycle.runtime

    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.recyclerview
    implementation libs.oplus.coui.toolbar
    implementation libs.oplus.coui.preference
    implementation libs.oplus.coui.clickablespan
    implementation libs.oplus.coui.poplist
    implementation libs.oplus.coui.panel

    // Koin for Android
    implementation(libs.koin)

    //cloudkit sdk  组件:https://doc.myoas.com/pages/viewpage.action?pageId=508124662
    domesticImplementation(libs.heytap.nearx.http)
    // 覆盖taphttp依赖，
    exportImplementation(libs.heytap.nearx.http) {
        /*#4118634: clientId sdk引入了android.permission.READ_PHONE_STATE权限，外销需去掉该sdk，否则有安规问题*/
        exclude group: 'com.heytap.baselib', module: 'clientId'
    }
    //公共库 （必须引入）
    domesticApi(libs.oplus.cloudkit.common) {
        // 移除ck中日志打捞，通过业务自行依赖
        exclude group: 'com.oplus', module: 'log'
        exclude group: 'com.oplus', module: 'log-domain-cn'
        exclude group: 'com.heytap.nearx', module: 'taphttp'
    }
    domesticApi libs.oplus.cloudkit.sync // 核心同步库（ 必须引入）
    domesticImplementation libs.oplus.cloudkit.guide// 同步开关打开引导库（可选），如果业务方没有开关入口 可不引入
    domesticImplementation libs.oplus.cloudkit.pay// 支付库（可选），如果业务方不需要进行购买空间引导和提示 则 可不引入

    exportApi(libs.oplus.cloudkit.common.oversea) {
        // 移除ck中日志打捞，通过业务自行依赖
        exclude group: 'com.oplus', module: 'log'
        exclude group: 'com.oplus', module: 'log-domain-oversea'
    }
    exportApi libs.oplus.cloudkit.sync.oversea
    exportImplementation libs.oplus.cloudkit.guide.oversea
    exportImplementation libs.oplus.cloudkit.pay.oversea


    //NearX-日志打捞sdk 主包
    implementation libs.oplus.log
    domesticImplementation libs.oplus.log.cn //NearX-日志打捞sdk 国内域名包（内销版本依赖）
    exportImplementation libs.oplus.log.oversea// 海外域名包（外销版本依赖）

    //必须接入的兼容开关和同步逻辑的旧有sdk
    implementation libs.oplus.cloud.base

    /*账号SDK*/
    implementation libs.oplus.stdid.sdk
    //欧盟版本也是这个依赖 https://doc.myoas.com/pages/viewpage.action?pageId=*********

    //账号ID SDK ，接入云同步二次校验能力SDK，并移除老版本账号SDK
    implementation(libs.oplus.account) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }

    //start push-Sdk
    implementation(libs.oplus.push) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        exclude group: 'com.squareup.okhttp3', module: 'okhttp-ws'
    }
    //end 切到正式环境要更改为debug，为了ALM release包不报错
    debugImplementation libs.oplus.test.env
    testImplementation libs.oplus.addon

    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
    implementation project(':common:RecorderLogBase')
}
