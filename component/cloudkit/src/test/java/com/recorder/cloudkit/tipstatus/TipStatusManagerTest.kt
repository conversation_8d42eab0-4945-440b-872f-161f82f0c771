package com.recorder.cloudkit.tipstatus

import android.content.Context
import android.os.Build
import android.os.Looper.getMainLooper
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.recorder.cloudkit.shadows.ShadowCOUIVersionUtil
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ICloudSwitchChangeListener
import java.util.concurrent.CopyOnWriteArrayList
import org.junit.*
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.Shadows.shadowOf
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowCOUIVersionUtil::class])
class TipStatusManagerTest {

    private var mContext: Context? = null
    private var mMockApplication: MockedStatic<BaseApplication>? = null
    private var mMockCloudSynStateHelper: MockedStatic<CloudSynStateHelper>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockApplication!!.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mContext)
        mMockCloudSynStateHelper = Mockito.mockStatic(CloudSynStateHelper::class.java)
        mMockCloudSynStateHelper!!.`when`<Boolean> { CloudSynStateHelper.isRegionCloudSupport() }?.thenReturn(true)
    }

    @After
    fun release() {
        mMockApplication?.close()
        mMockApplication = null
        mMockCloudSynStateHelper?.close()
        mMockCloudSynStateHelper = null
    }

    @Test
    fun should_when_init() {
        val supportArea = Whitebox.getInternalState<Boolean>(TipStatusManager.javaClass, "isSupportedCloud")
        Assert.assertFalse(supportArea)
        Whitebox.setInternalState(TipStatusManager.javaClass, "isSupportedCloud", true)

        TipStatusManager.init()

        var statusHelper = Whitebox.getInternalState<CloudSwitchStatusHelper>(TipStatusManager.javaClass, "mCloudSwitchStatusHelper")
        val isLogin = Whitebox.getInternalState<Boolean>(TipStatusManager.javaClass, "login")
        val syncSwitch = Whitebox.getInternalState<Int>(TipStatusManager.javaClass, "syncSwitch")
        val needSyncCount = Whitebox.getInternalState<Int>(TipStatusManager.javaClass, "needSyncCount")

        Assert.assertNotNull(statusHelper)
        Assert.assertFalse(isLogin)
        Assert.assertFalse(TipStatusManager.isCloudOn())
        Assert.assertTrue(syncSwitch == -1)
        Assert.assertTrue(needSyncCount == 0)

        shadowOf(getMainLooper()).idle()
        val tipManager = TipStatusManager
        Whitebox.invokeMethod<Unit>(tipManager, "initCloudSwitchChangeListener")
        statusHelper = Whitebox.getInternalState<CloudSwitchStatusHelper>(TipStatusManager.javaClass, "mCloudSwitchStatusHelper")
        Assert.assertNotNull(statusHelper)
    }

    @Test
    @Ignore
    fun should_when_setSyncSwitchState() {
        val tipManager = TipStatusManager
        // -1 --> 0
        Whitebox.invokeMethod<Unit>(tipManager, "setSyncSwitchState", 0)

        var syncSwitch = Whitebox.getInternalState<Int>(TipStatusManager.javaClass, "syncSwitch")
        Assert.assertTrue(syncSwitch == 0)

        // 0 --> 1
        Whitebox.invokeMethod<Unit>(tipManager, "setSyncSwitchState", 1)
        syncSwitch = Whitebox.getInternalState<Int>(TipStatusManager.javaClass, "syncSwitch")
        Assert.assertTrue(syncSwitch == 1)

        // 1 --> 0
        Whitebox.invokeMethod<Unit>(tipManager, "setSyncSwitchState", 0)
        syncSwitch = Whitebox.getInternalState<Int>(TipStatusManager.javaClass, "syncSwitch")
        Assert.assertTrue(syncSwitch == 0)

    }

    @Test
    fun should_when_getTipStatus() {
        shadowOf(getMainLooper()).idle()
        TipStatusManager.release()
        val tipStatusManger = TipStatusManager
        Assert.assertEquals(TipStatus.NONE, TipStatusManager.getTipStatus())

        Whitebox.setInternalState(TipStatusManager.javaClass, "isSupportedCloud", true)
        Assert.assertEquals(TipStatus.CLOUD_OFF, TipStatusManager.getTipStatus())

        Whitebox.invokeMethod<Unit>(tipStatusManger, "setSyncSwitchState", 1)
        Whitebox.setInternalState(TipStatusManager.javaClass, "syncResult", 0)
        Whitebox.setInternalState(TipStatusManager.javaClass, "login", true)
        Whitebox.setInternalState(TipStatusManager.javaClass, "hasCloudRequiredPermissions", false)
        Whitebox.setInternalState(TipStatusManager.javaClass, "syncSwitch", 1)
        Assert.assertEquals(TipStatus.NO_ALL_ACCESS_PERMISSION, tipStatusManger.getTipStatus())
        Assert.assertFalse(TipStatusManager.isSyncing())

        Whitebox.setInternalState(TipStatusManager.javaClass, "hasCloudRequiredPermissions", true)
        Assert.assertEquals(TipStatus.COMPLETED, tipStatusManger.getTipStatus())

        Whitebox.setInternalState(TipStatusManager.javaClass, "needSyncBackUpCount", 5)
        Assert.assertEquals(TipStatus.FAILURE, tipStatusManger.getTipStatus())

        Whitebox.setInternalState(TipStatusManager.javaClass, "syncResult", SyncErrorCode.RESULT_CANCEL)
        Assert.assertEquals(TipStatus.NONE, tipStatusManger.getTipStatus())

        Whitebox.setInternalState(TipStatusManager.javaClass, "syncResult", SyncErrorCode.UI_STATE_QUERYING)
        Assert.assertEquals(TipStatus.QUERY, tipStatusManger.getTipStatus())

        Whitebox.setInternalState(TipStatusManager.javaClass, "syncResult", SyncErrorCode.UI_STATE_SYNCING)
        Assert.assertEquals(TipStatus.SYNCING, tipStatusManger.getTipStatus())

        Whitebox.setInternalState(TipStatusManager.javaClass, "syncResult", SyncErrorCode.RESULT_INSUFFICIENT_SPACE)
        Assert.assertEquals(TipStatus.NO_CLOUD_SPACE, tipStatusManger.getTipStatus())

        Whitebox.setInternalState(TipStatusManager.javaClass, "syncResult", -999)
        Assert.assertEquals(TipStatus.FAILURE, tipStatusManger.getTipStatus())
    }

    @Ignore
    @Test
    fun should_when_registerCloudListener() {
        BaseApplication.sIsMainSystem = true
        val tipStatusManger = TipStatusManager
        val listener = object : ICloudSwitchChangeListener {
            override fun onToggle(isInitValue: Boolean, open: Boolean) {
            }
        }
        tipStatusManger.registerCloudListener(listener)

        val listenerList =
            Whitebox.getInternalState<CopyOnWriteArrayList<ICloudSwitchChangeListener>>(TipStatusManager.javaClass, "mCloudListenerSet")
        Assert.assertEquals(1, listenerList.size)

        tipStatusManger.unregisterCloudToggleListener(listener)

        val listenerListAfter =
            Whitebox.getInternalState<CopyOnWriteArrayList<ICloudSwitchChangeListener>>(TipStatusManager.javaClass, "mCloudListenerSet")
        Assert.assertEquals(0, listenerListAfter.size)
    }

    @Test
    fun should_when_isNeedShowGuide() {
        BaseApplication.sIsMainSystem = true
        val tipStatusManger = TipStatusManager
        shadowOf(getMainLooper()).idle()

        mMockCloudSynStateHelper!!.`when`<Boolean> { CloudSynStateHelper.isRegionCloudSupport() }?.thenReturn(false)
        Assert.assertFalse(tipStatusManger.isNeedShowGuide())

        Whitebox.setInternalState(TipStatusManager.javaClass, "isSupportedCloud", true)
        mMockCloudSynStateHelper!!.`when`<Boolean> { CloudSynStateHelper.isRegionCloudSupport() }?.thenReturn(true)
        Assert.assertTrue(tipStatusManger.isNeedShowGuide())

        Whitebox.setInternalState(TipStatusManager.javaClass, "login", true)
        Whitebox.setInternalState(TipStatusManager.javaClass, "syncSwitch", 1)
        Assert.assertFalse(tipStatusManger.isNeedShowGuide())
    }

    @Test
    fun should_when_isThreeMonthsLater() {
        val tipStatusManger = TipStatusManager

        val nullContext: Context? = null
        var result = Whitebox.invokeMethod<Boolean>(tipStatusManger, "isThreeMonthsLater", nullContext)
        Assert.assertFalse(result)

        result = Whitebox.invokeMethod<Boolean>(tipStatusManger, "isThreeMonthsLater", mContext)
        Assert.assertFalse(result)

    }

    @Test
    @Ignore
    fun should_reset_when_release() {
        TipStatusManager.release()

        Assert.assertNull(Whitebox.getInternalState<CloudSwitchStatusHelper>(TipStatusManager.javaClass, "mCloudSwitchStatusHelper"))
        Assert.assertFalse(Whitebox.getInternalState<Boolean>(TipStatusManager.javaClass, "isSupportedCloud"))
        Assert.assertEquals(-1, Whitebox.getInternalState<Int>(TipStatusManager.javaClass, "syncResult"))
        Assert.assertEquals(-1, Whitebox.getInternalState<Int>(TipStatusManager.javaClass, "syncSwitch"))
    }
}