/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveViewUtilTest
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(v-zhengt<PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.views.wave

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.DURATION_267
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.DURATION_283
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.getEnterHeightByTime
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.getLineScale
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.getOneWaveLineWidth
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.getOneWaveMinHeight
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.getOneWaveWidth
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
class WaveViewUtilTest {
    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun clear() {
        context = null
    }

    @Test
    fun should_notZero_when_getOneWaveWidth() {
        val context = context ?: return
        val waveWidth = context.getOneWaveWidth()
        Assert.assertNotEquals(0, waveWidth)
    }

    @Test
    fun should_notZero_when_getOneWaveLineWidth() {
        val context = context ?: return
        val waveWidth = context.getOneWaveLineWidth()
        Assert.assertNotEquals(0, waveWidth)
    }

    @Test
    fun should_notZero_when_getOneWaveMinHeight() {
        val context = context ?: return
        val waveWidth = context.getOneWaveMinHeight()
        Assert.assertNotEquals(0, waveWidth)
    }

    @Test
    fun should_correct_when_getLineScale() {
        Assert.assertTrue(getLineScale(0f) >= 0)
    }

    @Test
    fun should_correct_when_getEnterHeightByTime() {
        Assert.assertTrue(getEnterHeightByTime(100L, 8f, 10f) > 0f)
        Assert.assertTrue(getEnterHeightByTime(DURATION_267 + 100L, 8f, 10f) > 0f)
        Assert.assertTrue(getEnterHeightByTime(DURATION_267 + DURATION_283 + 1L, 8f, 10f) > 0f)
    }
}