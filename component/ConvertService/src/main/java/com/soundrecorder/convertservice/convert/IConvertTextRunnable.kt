/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IConvertTextRunnable
 * Description:
 * Version: 1.0
 * Date: 2025/3/10
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/10 1.0 create
 */

package com.soundrecorder.convertservice.convert

import com.soundrecorder.common.databean.ConvertStatus

interface IConvertTextRunnable {

    fun registerConvertUiCallback(convertUiCallback: IConvertCallback?)
    fun registerProgressCallback(progressCallback: IConvertTextRunnableProgress?)
    fun startConvert()
    fun cancelWork()
    fun getCurrentConvertStatus(): ConvertStatus?
    fun unregisterConvertUiCallback()
    fun release()
}