/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FileAsrResult
 * Description:
 * Version: 1.0
 * Date: 2025/4/3
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/4/3 1.0 create
 */

package com.soundrecorder.convertservice.bean

import androidx.annotation.Keep
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.databean.BeanConvertText

/**
 * {"code":0,
 * "message":"success",
 * "data":{"requestId":"82679647",
 * "originRequestId":"38439026",
 * "fileUrl":"recordsource/1743661736938/53943085/1743661738155.mp3","jobId":"20250403142903006404213081","taskStatus":1,
 * "originLanguage":"cn","detectLanguage":"cn",
 * "sentences":[
 * {"startOffset":0,"endOffset":106360,"roleId":"1","text":"Goodates war。",wordsPosList:[{text:"",start:8,end:9}]}
 * ]
 * }}
 */
@Keep
class FileAsrResult {
    var data: Data? = null

    @Keep
    class Data {
        var sentences: List<Item>? = null
    }

    fun toBeanConvertText(): BeanConvertText {
        var sublist: MutableList<BeanConvertText.SubItem>? = null
        if (data?.sentences.isNullOrEmpty().not()) {
            var seq = 0
            sublist = mutableListOf()
            val oneSecondMill = TimeUtils.TIME_ONE_SECOND.toFloat()
            data?.sentences?.forEach {
                sublist.add(
                    BeanConvertText.SubItem(
                        seq,
                        it.roleId?.toIntOrNull() ?: 0,
                        getTextFilterSensitiveWord(it) ?: "",
                        (it.startOffset / oneSecondMill).toString(),
                        (it.endOffset / oneSecondMill).toString()
                    ).apply {
                        /**给个空字符串，这样写入txt长度才对，兼容搬家到老版本数据解析、本地数据解析*/
                        rawText = ""
                    }
                )
                seq++
            }
        }
        return BeanConvertText("", "", "", "", sublist, null)
    }

    /**
     * 过滤敏感词
     */
    private fun getTextFilterSensitiveWord(item: Item): String? {
        if (item.wordsPosList.isNullOrEmpty()) {
            return item.text
        }
        var char: String?
        var textContent = item.text

        item.wordsPosList?.forEachIndexed { _, asrWord ->
            kotlin.runCatching {
                char = textContent?.substring(asrWord.start, asrWord.end + 1)
                if (char == asrWord.text) {
                    textContent = textContent?.replaceRange(
                            asrWord.start,
                            asrWord.end + 1,
                            getStarNum(char?.length ?: 0)
                    )
                } else {
                    DebugUtil.e("FileAsrResult", "getText replaceRange fail:")
                }
            }.onFailure {
                DebugUtil.e("FileAsrResult", "getText error:" + it.message)
            }
        }
        return textContent
    }

    private fun getStarNum(count: Int): String {
        var char = ""
        repeat(count) {
            char += "*"
        }
        return char
    }

    @Keep
    data class Item(val startOffset: Long, val endOffset: Long, val roleId: String?, val text: String?) {
        var wordsPosList: List<Word>? = null
    }

    @Keep
    data class Word(val text: String?, val start: Int, val end: Int)
}