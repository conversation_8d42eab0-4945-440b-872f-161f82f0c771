/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/01/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.convertservice.shadows;

import com.soundrecorder.convertservice.convert.NewConvertResultUtil;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(NewConvertResultUtil.class)
public class ShadowNewConvertResultUtil {

    @Implementation
    public static boolean isExternalStorageWritable() {
        return true;
    }

    @Implementation
    public static boolean isExternalStorageReadable() {
        return true;
    }
}
