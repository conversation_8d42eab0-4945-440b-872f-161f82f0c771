/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/11/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.recorder.feedback

import android.app.Activity
import android.content.Context
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.oplus.recorder.questionnaire.R
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.ViewUtils

object FeedPermissionUtils {

    private const val TAG = "FeedPermissionUtils"

    private const val KEY_STORAGE_FEEDBACK_NET = "storage_switch_feedback_net"



    /**
     * 判断是否申请网络权限
     */
    @JvmStatic
    fun hasGetInternetPermission(context: Context?): Boolean {
        return StorageManager.getIntPref(context, KEY_STORAGE_FEEDBACK_NET, 0) == 1
    }

    /**
     * 设置是否申请网络权限
     */
    @JvmStatic
    fun setInternetPermission(context: Context?, value: Int) {
        StorageManager.setIntPref(context, KEY_STORAGE_FEEDBACK_NET, value)
    }

    @JvmStatic
    fun showFeedInternetPermissionDialog(activity: Activity, listener: FeedPermissionDialogListener): AlertDialog {
        val builder = COUIAlertDialogBuilder(activity)
        return builder.setTitle(activity.resources.getString(R.string.privacy_policy_helpe_notice_title,
                activity.resources.getString(com.soundrecorder.common.R.string.app_name_main)))
                .setMessage(activity.getString(R.string.privacy_policy_helpe_notice_content))
                .setBlurBackgroundDrawable(true)
                .setPositiveButton(com.soundrecorder.common.R.string.save_continue) { _, _ ->
                    listener.onClick(true)
                }.setNegativeButton(com.soundrecorder.common.R.string.cancel) { _, _ ->
                    listener.onClick(false)
                }.setOnCancelListener {
                    DebugUtil.d(TAG, "Permission dialog on cancel")
                    listener.onClick(false) // back键响应取消按钮点击事件
                }.show().apply {
                    setCancelable(true)
                    setCanceledOnTouchOutside(false)
                    ViewUtils.updateWindowLayoutParams(window)
                }
    }


    interface FeedPermissionDialogListener {
        fun onClick(isOk: Boolean)
    }
}