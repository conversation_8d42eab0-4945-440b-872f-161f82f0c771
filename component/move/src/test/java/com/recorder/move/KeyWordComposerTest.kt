package com.recorder.move

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.recorder.move.shadows.ShadowBaseUtils
import com.recorder.move.shadows.ShadowFeatureOption
import com.recorder.move.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.databean.KeyWord
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowBaseUtils::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class KeyWordComposerTest {

    private var composer: KeyWordComposer? = null

    @Before
    fun setUp() {
        composer = KeyWordComposer()
    }

    @Test
    fun should_return_String_getTag() {
        val tag = composer?.getTag()

        Assert.assertNotNull(tag)
        Assert.assertEquals("key_word", tag)
    }

    @Test
    fun should_return_string_getXmlName() {
        val xmlName = composer?.getXmlName()

        Assert.assertNotNull(xmlName)
        Assert.assertEquals("key_word.xml", xmlName)
    }


    @Test
    fun should_return_bool_startCompose() {
        val startResult = composer?.startCompose()
        val endResult = composer?.endCompose()
        Assert.assertEquals(true, startResult)
        Assert.assertEquals(true, endResult)
    }

    @Test
    fun should_return_string_getXmlInfo() {
        val keyWord = KeyWord("测试", 0.5f)
        composer?.startCompose()
        composer?.addData(keyWord)
        composer?.endCompose()
        val result = composer?.getXmlInfo()

        Assert.assertNotNull(result)
    }
}