/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - IFileUploader.kt
 ** Description: IFileUploader.
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/10    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.manager

import com.soundrecorder.share.normal.link.manager.listener.IFileUploaderListener
import java.io.File

interface IFileUploader {

    fun startUpload(file: File, url: String, fileId: String, shareId: String, recordId: Long)

    fun registerListener(listener: IFileUploaderListener)

    fun unregisterListener(listener: IFileUploaderListener)
}