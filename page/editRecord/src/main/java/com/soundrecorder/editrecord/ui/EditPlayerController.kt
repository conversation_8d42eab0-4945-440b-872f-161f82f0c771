/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditPlayerController
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.ui

import android.media.AudioManager
import android.net.Uri
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.getValueWithDefault
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.base.PlayerHelperBasicCallback
import com.soundrecorder.common.buryingpoint.CuttingStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.editrecord.R
import com.soundrecorder.player.WavePlayerController
import com.soundrecorder.player.speaker.ToDoInSpeakerReceiver
import com.soundrecorder.player.status.PlayStatus

class EditPlayerController(playerCallback: PlayerHelperBasicCallback?) :
    WavePlayerController(playerCallback),
    ToDoInSpeakerReceiver {

    override val TAG = "EditPlayerController"

    var lastPlayStartTime = MutableLiveData<Long>()
    var lastPlayProgressTime: Long = 0

    private var mIsFirstSet = true

    fun setLastPlayTime(time: Long) {
        if (mIsFirstSet && time == 0L) {
            mIsFirstSet = false
            return
        }
        lastPlayStartTime.postValueSafe(time)
        lastPlayProgressTime = time
    }

    fun prepareToPlay(playUri: Uri?): Boolean {
        playController?.setPlayUri(playUri)
        return playController?.prepareToPlay(getSeekTime()) ?: false
    }

    override fun onPlayError(extra: Int) {
        val isAACError = dealAACErrorSeekToDuration(extra)
        if (!isAACError) {
            releasePlay()
            val context = BaseApplication.getAppContext()
            ToastManager.showShortToast(context, context.getString(com.soundrecorder.common.R.string.player_error))
        }
        super.onPlayError(extra)
    }

    override fun onActionComplete(action: String) {
        when (action) {
            PlayStatus.ACTION_PREPARE ->
                setLastPlayTime(currentTimeMillis.getValueWithDefault())
            PlayStatus.ACTION_CONTINUE ->
                setLastPlayTime(currentTimeMillis.getValueWithDefault())
            else -> super.onActionComplete(action)
        }
    }

    override fun getAudioStreamType(): Int {
        return AudioManager.STREAM_MUSIC
    }

    override fun doSeekTime(timeMills: Long, correctCurTime: Boolean) {
        super.doSeekTime(timeMills, correctCurTime)
        setLastPlayTime(timeMills)
    }

    fun seekRelease() {
        releasePlay()
        playerState.postValueSafe(PlayStatus.PLAYER_STATE_HALTON)
    }

    override
    fun doWithBlueTooth(status: Int) {
        DebugUtil.i(TAG, "doWithBlueTooth, status: $status")
    }

    override
    fun doWithWired(state: Int) {
        DebugUtil.i(TAG, "doWithWired, state: $state")
    }

    override
    fun doWithReceiveNoisy() {
        DebugUtil.i(TAG, " doWithReceiveNoisy! ")
        pausePlay()
    }

    override fun doContinuePlay() {
        super.doContinuePlay()
        CuttingStaticsUtil.addCutTrimPlayPause(RecorderUserAction.VALUE_TRIM_PLAY)
    }

    override fun doPausePlay() {
        super.doPausePlay()
        CuttingStaticsUtil.addCutTrimPlayPause(RecorderUserAction.VALUE_TRIM_PAUSE)
    }
}