This document contains licenses and notices for open source software used in this product.

Warranty Disclaimer
The open source software in this product is offered without any warranty, including but not limited to the general usability or fitness for a particular purpose.

Open Source Software Licensed under the BSD-2-Clause License:
--------------------------------------------------------------------
1. gifdecoder 4.12.0
Copyright (c) 2010 <PERSON>; http:  recursive-design.com
Copyright 2012 Jake <PERSON>
Copyright 2014 Google; Inc. All rights reserved.
copyright asserted on the source code of this class. May be used for any
Copyright (c) 2013 Xcellent Creations; Inc.
Copyright 2013 Bump Technologies; Inc. All rights reserved.
2. commonmark 0.17.0
3. rebound 0.3.8
4. glide 4.12.0
Copyright (c) 2010 <PERSON>; http:  recursive-design.com
Copyright 2012 Jake <PERSON>
Copyright 2014 Google; Inc. All rights reserved.
copyright asserted on the source code of this class. May be used for any
Copyright (c) 2013 Xcellent Creations; Inc.
Copyright 2013 Bump Technologies; Inc. All rights reserved.
5. disklrucache 4.12.0
Copyright (c) 2010 <PERSON>; http:  recursive-design.com
Copyright 2012 Jake Wharton
Copyright 2014 Google; Inc. All rights reserved.
copyright asserted on the source code of this class. May be used for any
Copyright (c) 2013 Xcellent Creations; Inc.
Copyright 2013 Bump Technologies; Inc. All rights reserved.
6. annotations 4.12.0
Copyright (c) 2010 Dave Perrett; http:  recursive-design.com
Copyright 2012 Jake Wharton
Copyright 2014 Google; Inc. All rights reserved.
copyright asserted on the source code of this class. May be used for any
Copyright (c) 2013 Xcellent Creations; Inc.
Copyright 2013 Bump Technologies; Inc. All rights reserved.
Terms of the BSD-2-Clause:
--------------------------------------------------------------------
Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.



Open Source Software Licensed under the BSD-3-Clause License:
--------------------------------------------------------------------
1. protobuf-java 4.29.0
Terms of the BSD-3-Clause:
--------------------------------------------------------------------
Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

3. Neither the name of the copyright holder nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.



Open Source Software Licensed under the Apache-2.0 License:
--------------------------------------------------------------------
1. sqlite-framework 2.0.0
2. gifdecoder 4.12.0
Copyright (c) 2010 Dave Perrett; http:  recursive-design.com
Copyright 2012 Jake Wharton
Copyright 2014 Google; Inc. All rights reserved.
copyright asserted on the source code of this class. May be used for any
Copyright (c) 2013 Xcellent Creations; Inc.
Copyright 2013 Bump Technologies; Inc. All rights reserved.
3. kotlin-stdlib-common 1.9.22
Copyright (c) 1996 - 2021; Daniel Stenberg; <<EMAIL>>; and many
4. kotlin-parcelize-runtime 1.9.22
Copyright (c) 1996 - 2021; Daniel Stenberg; <<EMAIL>>; and many
5. annotation 1.3.0
6. Markwon v4.6.0
Copyright 2019 Dimitry Ivanov (<EMAIL>)
7. kotlinx-coroutines-android 1.7.1
8. constraintlayout-solver 2.0.1
9. versionedparcelable 1.1.1
10. Markwon v3.0.0
Copyright 2017 Dimitry Ivanov (<EMAIL>)
11. gson 2.10.1
12. coordinatorlayout 1.1.0
13. interpolator 1.0.0
14. tagsoup 1.2.1
15. appcompat 1.6.1
16. lifecycle-extensions 2.2.0
17. sqlite-framework 2.1.0
18. databinding-adapters 8.6.0
19. logging-interceptor 4.9.0
Copyright 2019 Square; Inc.
20. converter-gson 2.7.0
Copyright 2013 Square; Inc.
Copyright (C) 2012 Square; Inc.
21. room-ktx 2.4.2
22. customview 1.1.0
23. sqlite 2.0.0
24. localbroadcastmanager 1.0.0
25. legacy-support-v4 1.0.0
26. webkit 1.4.0
27. swiperefreshlayout 1.0.0
28. koin-android 3.4.3
29. activity 1.8.0
30. kotlin-stdlib 1.9.22
Copyright (c) 1996 - 2021; Daniel Stenberg; <<EMAIL>>; and many
31. activity 1.6.0
32. core 1.13.0
33. kotlin-stdlib-common 1.9.23
Copyright (c) 1996 - 2021; Daniel Stenberg; <<EMAIL>>; and many
34. annotation-experimental 1.3.0
35. lifecycle-livedata 2.6.2
36. okio 3.9.0
Copyright 2013 Square; Inc.
37. slidingpanelayout 1.0.0
38. palette 1.0.0
39. core-ktx 1.13.0
40. drawerlayout 1.1.1
41. dynamicanimation 1.0.0
42. koin-core 3.4.3
43. fragment-ktx 1.5.7
44. startup-runtime 1.1.1
45. preference 1.1.1
46. Markwon v4.3.0
Copyright 2019 Dimitry Ivanov (<EMAIL>)
47. transition 1.4.1
48. collection-ktx 1.1.0
49. annotations 23.0.0
50. coil 2.4.0
51. lifecycle-process 2.6.1
52. converter-protobuf 2.9.0
Copyright 2013 Square; Inc.
Copyright (C) 2012 Square; Inc.
53. paging-common-ktx 3.1.1
54. annotation-experimental 1.4.0
55. core 1.9.0
56. vectordrawable-animated 1.1.0
57. stfalconimageviewer 9081fa1
Copyright (C) 2018 stfalcon.com
58. media 1.0.0
59. vectordrawable 1.1.0
60. kotlinx-coroutines-android 1.6.4
61. cursoradapter 1.0.0
62. asynclayoutinflater 1.0.0
63. retrofit 2.9.0
Copyright 2013 Square; Inc.
Copyright (C) 2012 Square; Inc.
64. drawerlayout 1.0.0
65. sqlite 2.1.0
66. fragment 1.5.7
67. kotlinx-coroutines-core 1.6.4
68. constraintlayout 2.0.1
69. logging-interceptor 4.2.2
Copyright 2019 Square; Inc.
70. okio-jvm 3.9.0
Copyright 2013 Square; Inc.
71. converter-gson 2.9.0
Copyright 2013 Square; Inc.
Copyright (C) 2012 Square; Inc.
72. core 4.6.2
Copyright 2019 Dimitry Ivanov (<EMAIL>)
73. paging-common 3.1.1
74. recyclerview 1.2.1
75. constraintlayout 2.1.4
76. lifecycle-livedata-ktx 2.6.2
77. transition-ktx 1.4.1
78. paging-runtime 3.1.1
79. legacy-support-core-utils 1.0.0
80. customview 1.0.0
81. kotlin-stdlib-jdk7 1.9.20
Copyright (c) 1996 - 2021; Daniel Stenberg; <<EMAIL>>; and many
82. viewpager 1.0.0
83. legacy-support-core-ui 1.0.0
84. exifinterface 1.3.6
85. lifecycle-runtime 2.6.2
86. Markwon v4.6.1
Copyright 2019 Dimitry Ivanov (<EMAIL>)
87. savedstate-ktx 1.2.1
88. retrofit 2.7.0
Copyright 2013 Square; Inc.
Copyright (C) 2012 Square; Inc.
89. glide 4.12.0
Copyright (c) 2010 Dave Perrett; http:  recursive-design.com
Copyright 2012 Jake Wharton
Copyright 2014 Google; Inc. All rights reserved.
copyright asserted on the source code of this class. May be used for any
Copyright (c) 2013 Xcellent Creations; Inc.
Copyright 2013 Bump Technologies; Inc. All rights reserved.
90. material 1.6.0
91. annotations 13.0
92. activity-ktx 1.8.0
93. activity-ktx 1.6.1
94. cardview 1.0.0
95. lifecycle-common 2.6.2
96. less 3.12.2
Copyright (c) Microsoft Corporation. All rights reserved.
(c) 2009-2014 [Alexis Sellier](http:  cloudhead.io)   The Core Less Team
(c) 2009-2016 [Alexis Sellier](http:  cloudhead.io)   The Core Less Team
Copyright (C) 2012-2018 by various contributors (see AUTHORS)
Copyright JS Foundation and other contributors; https:  js.foundation
Copyright (c) 2019 TypeScript ESLint and other contributors
(c) 2009-2017 [Alexis Sellier](http:  cloudhead.io)   The Core Less Team
copyright (Nicholas C. Zakas)
Copyright (c) 2014-2017 TJ Holowaychuk  lt;<EMAIL> gt;
Copyright (c) 2009-2011 Alexis Sellier
Copyright (c) 2014 TJ Holowaychuk <<EMAIL>>
Copyright (c) 2009-2011; Mozilla Foundation and contributors
Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)
Copyright (c) 2009-2013 Alexis Sellier   The Core Less Team
97. viewpager2 1.0.0
98. savedstate 1.2.1
99. collection 1.2.0
100. kotlin-stdlib-jdk8 1.9.20
Copyright (c) 1996 - 2021; Daniel Stenberg; <<EMAIL>>; and many
101. room-runtime 2.4.2
102. material 1.11.0
103. window 1.1.0-alpha02
104. fragment 1.3.6
105. kotlinx-coroutines-core-jvm 1.6.4
106. databinding-runtime 8.6.0
107. documentfile 1.0.0
108. paging-runtime-ktx 3.1.1
109. preference 1.1.0
110. kotlin-stdlib-jdk7 1.9.22
Copyright (c) 1996 - 2021; Daniel Stenberg; <<EMAIL>>; and many
111. kotlinx-coroutines-core-jvm 1.7.1
112. coil-base 2.4.0
113. kotlin-stdlib 1.9.23
Copyright (c) 1996 - 2021; Daniel Stenberg; <<EMAIL>>; and many
114. commons-codec 1.15
Copyright (c) 1999-2003 The Apache Software Foundation.  All rights
Copyright (c) 2001-2003 The Apache Software Foundation.  All rights
115. lifecycle-livedata 2.6.1
116. print 1.0.0
117. disklrucache 4.12.0
Copyright (c) 2010 Dave Perrett; http:  recursive-design.com
Copyright 2012 Jake Wharton
Copyright 2014 Google; Inc. All rights reserved.
copyright asserted on the source code of this class. May be used for any
Copyright (c) 2013 Xcellent Creations; Inc.
Copyright 2013 Bump Technologies; Inc. All rights reserved.
118. lifecycle-viewmodel 2.6.1
119. lottie 6.3.0
120. lifecycle-viewmodel-savedstate 2.6.2
121. constraintlayout 2.1.1
122. hap-toolkit 1.9.16
123. activity 1.6.1
124. material 1.7.0
125. core-ktx 1.9.0
126. kotlinx-coroutines-core 1.7.1
127. room-common 2.4.2
128. annotation-jvm 1.6.0
129. recyclerview 1.2.0
130. transition 1.2.0
131. loader 1.0.0
132. core-common 2.2.0
133. appcompat-resources 1.6.1
134. sqlite-framework 2.2.0
135. window-testing 1.0.0
136. koin-core-jvm 3.4.3
137. kotlin-android-extensions-runtime 1.9.22
Copyright (c) 1996 - 2021; Daniel Stenberg; <<EMAIL>>; and many
138. annotations 4.12.0
Copyright (c) 2010 Dave Perrett; http:  recursive-design.com
Copyright 2012 Jake Wharton
Copyright 2014 Google; Inc. All rights reserved.
copyright asserted on the source code of this class. May be used for any
Copyright (c) 2013 Xcellent Creations; Inc.
Copyright 2013 Bump Technologies; Inc. All rights reserved.
139. room-runtime 2.3.0
140. window-java 1.0.0
141. viewbinding 8.6.0
142. lifecycle-service 2.6.1
143. sqlite 2.2.0
144. lifecycle-livedata-core-ktx 2.6.2
145. resourceinspection-annotation 1.0.1
Terms of the Apache-2.0:
--------------------------------------------------------------------
Apache License
Version 2.0, January 2004
http://www.apache.org/licenses/

TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

1. Definitions.

"License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

"Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

"Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

"You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

"Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

"Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

"Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

"Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

"Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

"Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

     (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

     (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

     (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

     (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

     You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability.

END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!)  The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.



Open Source Software Licensed under the MIT License:
--------------------------------------------------------------------
1. prettier 2.1.2
Copyright    James Long and contributors
2. prettier-plugin-quickapp 0.1.0
3. babel-eslint 10.0.1
Copyright (c) 2014-2016 Sebastian McKenzie <<EMAIL>>
4. less-loader 7.0.1
Copyright JS Foundation and other contributors
Copyright (c) 2015 Tobias Koppers
5. clipboard 2.0.6
Copyright Zeno Rocha
Copyright (c) Zeno Rocha
6. eslint 5.16.0
Copyright (c) 2015 Simon Boudrias (twitter: @vaxilart)
(c) 2013 [Ramesh Nair](http:  www.hiddentao.com )
(c) 2013 Mikola Lysenko. MIT License
(C) 2013 [Yusuke Suzuki](http:  github.com Constellation)
Copyright (c) 2015 DC <<EMAIL>>
Copyright (c) 2010; Linden Research; Inc.
Copyright OpenJS Foundation and other contributors; <www.openjsf.org>
Copyright (C) 2012 by Vitaly Puzrin
copyright (#7497) (Nicholas C. Zakas)
Copyright JS Foundation and other contributors; https:  js.foundation
Copyright (c) 2012; Artur Adib <<EMAIL>>
Copyright (C) 2012-2015 Mariusz Nowak (www.medikoo.com)
Copyright (c) 2014 Mathias Buus
(C) 2012 [Yusuke Suzuki](http:  github.com Constellation)
Copyright (c) Isaac Z. Schlueter
Copyright (c) 2013 Max Ogden_
Copyright (c) 2014 Simon Boudrias
Copyright Joyent; Inc. and other Node contributors. See LICENCE file for details.
Copyright Joyent; Inc. and other Node contributors. All rights reserved.
7. lint-staged 10.4.0
Copyright (c) 2016 Andrey Okonetchnikov
8. databinding-ktx 8.6.0
9. tts-server-android 0.6_202302190935
10. eslint-plugin-hybrid 0.0.5
Copyright (c) 2017 YONG QING
Terms of the MIT:
--------------------------------------------------------------------
MIT License

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.



