/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/8/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.center.localsync

import com.soundrecorder.browsefile.search.load.center.CenterDbManager
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertBean
import com.soundrecorder.base.utils.DebugUtil

object CenterSyncManager {

    private const val TAG = "CenterSyncManager"

    //todo record表中数据如果有需要则同步
    fun doReveal(totalList: List<SearchInsertBean>?, callDmpSyncWhenEmpty: Boolean) {
        val newTotalList = sortTotalList(totalList)
        val newSyncStr = calculateNewSyncStr(newTotalList)
        val savedSyncStr = CenterDbUtils.getSyncedMediaInfo()
        DebugUtil.d(
            TAG,
            "doReveal, callDmpSyncWhenEmpty= $callDmpSyncWhenEmpty totalList.size is ${totalList?.size} savedSyncStr is $savedSyncStr, newSyncStr is $newSyncStr")

        if ((newSyncStr.isNullOrBlank().not()) && (savedSyncStr == newSyncStr)) {
            //the saved str is the same with the new one if they not null, do nothing
        } else if (savedSyncStr.isNullOrEmpty()) {
            /*
             * callDmpSyncWhenEmpty is true, notify dmp to full-sync, avoid recording clear app-data but dmp not receive notify
             * the saved str is null, but the new str is not null, just update all and save it
             */
            val result =
                if (callDmpSyncWhenEmpty) {
                    CenterDbManager.notifyDmpAllSync()
                } else {
                    CenterDbManager.insertOrUpdateDmp(newTotalList, false)
                }
            if (result) {
                CenterDbUtils.saveSyncedMediaInfo(newSyncStr)
            }
        } else if (newSyncStr.isNullOrEmpty()) {
            //the saved str is not null, but the new one is null, just delete the whole items
            val bean = CenterLocalStorageBean.convertStrToBean(savedSyncStr)
            DebugUtil.d(TAG, "doReveal, delete convert list is ${bean?.originList}")
            if (!bean?.originList.isNullOrEmpty()) {
                val deleteList = mutableListOf<Long>().also {
                    for (item in bean!!.originList!!) {
                        it.add(item.mediaId)
                    }
                }

                val result = CenterDbManager.deleteByMediaId(deleteList, false)
                if (result) {
                    CenterDbUtils.saveSyncedMediaInfo(newSyncStr)
                }
            }
        } else {
            //both the two str is not null, just diff
            val bean = CenterLocalStorageBean.convertStrToBean(savedSyncStr)
            DebugUtil.d(TAG, "doReveal, diff convert list is ${bean?.originList?.size} - ${bean?.originList}")
            if (!bean?.originList.isNullOrEmpty()) {
                val result = diffListAndSync(newTotalList!!, bean!!.originList!!)
                if (result) {
                    CenterDbUtils.saveSyncedMediaInfo(newSyncStr)
                }
            }
        }
    }

    private fun sortTotalList(totalList: List<SearchInsertBean>?): MutableList<SearchInsertBean>? {
        return if (totalList.isNullOrEmpty()) {
            null
        } else {
            totalList.toMutableList().apply {
                sortByDescending { it.id }
            }
        }
    }

    /**
     * calculate the new sync str for ItemBrowseRecordViewModel list
     */
    private fun calculateNewSyncStr(totalList: List<SearchInsertBean>?): String? {
        return if (totalList.isNullOrEmpty()) {
            null
        } else {
            val newList = CenterLocalStorageManager.createStorageItemList(totalList, false)
            CenterLocalStorageBean(newList).toString()
        }
    }

    /**
     * calculate the diff update and delete list, then notify search center
     */
    private fun diffListAndSync(
        totalList: MutableList<SearchInsertBean>,
        originList: List<CenterLocalStorageItem>
    ): Boolean {
        val updateList = mutableListOf<SearchInsertBean>()
        val deleteList = mutableListOf<Long>()
        diffList(totalList, originList, updateList, deleteList)

        return if (updateList.isEmpty() && deleteList.isEmpty()) {
            false
        } else {
            (CenterDbManager.insertOrUpdateDmp(updateList, false) &&
                    CenterDbManager.deleteByMediaId(deleteList, false))
        }
    }

    private fun diffList(
        totalList: MutableList<SearchInsertBean>,
        originList: List<CenterLocalStorageItem>,
        updateList: MutableList<SearchInsertBean>,
        deleteList: MutableList<Long>
    ) {
        var curNewPos = 0
        var curOldPos = 0
        var curNewItem = getCurItem(totalList, curNewPos)
        var curOldItem = getCurItem(originList, curOldPos)
        while (curNewItem != null || curOldItem != null) {
            when ((curNewItem?.id ?: -1).compareTo(curOldItem?.mediaId ?: -1)) {
                0 -> { //both items are not null, and they are the same item
                    // the same item, just compare the lastModify time
                    if (curNewItem?.date_modified == curOldItem?.lastModify) {
                        //nothing changed, no need to update/delete
                    } else if (curNewItem != null) {
                        //both items are not null, item has changed
                        updateList.add(curNewItem)
                    }

                    curNewPos++
                    curNewItem = getCurItem(totalList, curNewPos)

                    curOldPos++
                    curOldItem = getCurItem(originList, curOldPos)
                }
                1 -> { // mediaId of the new item is bigger than the old, or the old is null
                    //new item has been inserted
                    updateList.add(curNewItem!!)

                    curNewPos++
                    curNewItem = getCurItem(totalList, curNewPos)
                }
                -1 -> { // mediaId of the old item is bigger than the new, or the new is null
                    // old item has been deleted
                    deleteList.add(curOldItem!!.mediaId)

                    curOldPos++
                    curOldItem = getCurItem(originList, curOldPos)
                }
            }
        }
    }

    private fun <T> getCurItem(totalList: List<T>, pos: Int): T? {
        return try {
            totalList[pos]
        } catch (e: Exception) {
            DebugUtil.d(TAG, "getCurItem: pos is $pos, error is ${e.message}")
            null
        }
    }
}