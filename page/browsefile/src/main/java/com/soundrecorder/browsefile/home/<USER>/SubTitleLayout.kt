package com.soundrecorder.browsefile.home.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.view.MotionEvent
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ITipStatus
import com.soundrecorder.modulerouter.cloudkit.tipstatus.OnLinkTextClickListener

open class SubTitleLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "SubTitleLayout"
        private const val FLOAT_14 = 14.0f
    }

    private var mNewAlpha: Float = 0f
    private var descTv: TextView? = null
    private var tvRecycleSubtitleDesc: TextView? = null
    private var eventBtn: TextView? = null
    private var clickListener: OnLinkTextClickListener? = null
    private var tipStatusState: Int = ITipStatus.STATE_NONE

    override fun onFinishInflate() {
        super.onFinishInflate()
        initView()
    }


    private fun initView() {
        descTv = findViewById(R.id.subtitle_desc)
        tvRecycleSubtitleDesc = findViewById(R.id.tv_recycle_subtitle_desc)
        eventBtn = findViewById(R.id.subtitle_event)
        eventBtn?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, FLOAT_14)

        tvRecycleSubtitleDesc?.isVisible = false
        eventBtn?.setOnClickListener {
            clickListener?.onClick(tipStatusState)
        }
    }


    fun updateSubTitleClickState(alpha: Float) {
        mNewAlpha = alpha
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (mNewAlpha <= 0) {
            return false
        }
        return super.onTouchEvent(event)
    }

    /**
     * 设置标题的图片
     */
    fun setCompoundStartDrawables(@DrawableRes resId: Int) {
        descTv?.setCompoundDrawablesRelativeWithIntrinsicBounds(resId, 0, 0, 0)
    }

    /**
     * 设置标题内容
     */
    fun setTitleText(descText: String, eventText: String = "") {
        if (TextUtils.equals(descText, descTv?.text) && TextUtils.equals(eventText, eventBtn?.text)) {
            DebugUtil.i(TAG, "setTitleText sameTitle desc:$descText event:$eventText")
            return
        }
        descTv?.text = descText
        eventBtn?.text = eventText
    }

    /**
     * 设置文字点击事件
     */
    fun setOnLinkTextClickListener(statusState: Int?, listener: OnLinkTextClickListener?) {
        this.tipStatusState = statusState ?: ITipStatus.STATE_NONE
        this.clickListener = listener
    }

    /**
     * 设置回收站 subtitle
     */
    fun setRecycleSubTitleText(descText: String) {
        if (TextUtils.equals(descText, tvRecycleSubtitleDesc?.text)) {
            DebugUtil.i(TAG, "setRecycleSubTitleText sameTitle desc:$descText")
            return
        }
        tvRecycleSubtitleDesc?.text = descText
    }

    /**
     * isVisible 是否显示回收站subtitle
     * true:显示回收站subtitle，隐藏全部录音subtitle
     * false:隐藏回收站subtitle，同时显示全部录音subtitle
     */
    fun setRecycleSubtitleVisible(isVisible: Boolean) {
        tvRecycleSubtitleDesc?.isVisible = isVisible
        descTv?.isVisible = !isVisible
        eventBtn?.isVisible = !isVisible
    }
}