/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:Edit page for recording grouping
 ** Version: 1.0
 ** Date : 2025/01/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  ******** 2025/01/20      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.view

import android.graphics.Color
import android.graphics.drawable.LayerDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.doOnLayout
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.edittext.COUICardSingleInputView
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.soundrecorder.base.utils.KeyboardUtils.hideSoftInput
import com.soundrecorder.base.utils.KeyboardUtils.showSoftInput
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.view.group.entity.RecordingGroupData
import com.soundrecorder.browsefile.home.view.group.util.GroupManageUtils
import com.soundrecorder.browsefile.home.view.group.util.LocalEditingGroupChannel
import com.soundrecorder.browsefile.home.view.group.util.RecordGroupEditViewModel
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.utils.AudioNameUtils.MAX_CHAR_LENGTH
import com.soundrecorder.common.utils.SubRecorderTextUtils
import com.soundrecorder.common.widget.TalkBackEditTextView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

internal class GroupEditFragment : COUIPanelFragment() {

    companion object {
        const val TAG = "GroupEditFragment"
        private const val MAX_GROUP_NAME_LENGTH: Int = 20
        private const val MAX_ALPHA_VALUE = 255
        private const val SHOW_INPUT_DELAY = 100L

        private const val ARGUMENTS_EXTRA_GROUP = "arguments_extra_group"
        private const val ARGUMENTS_EXTRA_FROM = "arguments_extra_from"

        fun show(fm: FragmentManager, groupInfo: GroupInfo? = null) {
            val couiBottomSheetDialogFragment = COUIBottomSheetDialogFragment()
            couiBottomSheetDialogFragment.setMainPanelFragment(newInstance(groupInfo))
            couiBottomSheetDialogFragment.show(fm, GroupEditFragment::class.java.name)
        }

        private fun newInstance(groupInfo: GroupInfo?): GroupEditFragment {
            val fragment = GroupEditFragment()
            val args = Bundle()
            args.putParcelable(ARGUMENTS_EXTRA_GROUP, groupInfo)
            fragment.arguments = args
            return fragment
        }
    }

    private var groupInfo: GroupInfo? = null
    private var from: Int = 0
    private var saveItem: MenuItem? = null
    private var withIconCardSingleInputView: COUICardSingleInputView? = null
    private var inputIcon: ImageView? = null
    private var renamePrompts: TextView? = null
    private var redLine: View? = null
    private var mTextWatcherProxy: TextWatcher? = null

    private val runnable = Runnable {
        withIconCardSingleInputView?.editText?.showSoftInput()
    }

    private val groupEditViewModel by viewModels<RecordGroupEditViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        groupInfo = arguments?.getParcelable(ARGUMENTS_EXTRA_GROUP)

        from = arguments?.getInt(ARGUMENTS_EXTRA_FROM, 0) ?: 0
        initEditTextWatcher()
    }

    override fun initView(panelView: View?) {
        super.initView(panelView)
        val bgColor =
            COUIContextUtil.getAttrColor(
                context,
                com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard
            )
        panelView?.setBackgroundColor(bgColor)
        LayoutInflater.from(context)
            .inflate(R.layout.fragment_group_edit, contentView as? ViewGroup, true)
        initToolbar()
        initSingleInputView(panelView)
        initGridCovers(panelView)
        renamePrompts = panelView?.findViewById(R.id.tv_group_rename_prompts)
        redLine = panelView?.findViewById(R.id.red_line)
        initEditTalkBack()
        initShowKeyboard()
        initTextFilter()
    }

    private fun initShowKeyboard() {
        withIconCardSingleInputView?.viewTreeObserver?.addOnWindowFocusChangeListener {
            if (it) {
                showInputKeyboard()
            }
        }
    }

    private fun initGridCovers(root: View?) {
        if (root == null) {
            return
        }

        covers.addAll(
            listOf(
                Cover(
                    RecordingGroupData.IC_COVER_PURE_RED,
                    false,
                    root.findViewById(R.id.group_edit_cover_red)
                ),
                Cover(
                    RecordingGroupData.IC_COVER_PURE_YELLOW,
                    false,
                    root.findViewById(R.id.group_edit_cover_yellow)
                ),
                Cover(
                    RecordingGroupData.IC_COVER_PURE_ORANGE,
                    false,
                    root.findViewById(R.id.group_edit_cover_orange)
                ),
                Cover(
                    RecordingGroupData.IC_COVER_PURE_GREEN,
                    false,
                    root.findViewById(R.id.group_edit_cover_green)
                ),
                Cover(
                    RecordingGroupData.IC_COVER_PURE_AZURE,
                    false,
                    root.findViewById(R.id.group_edit_cover_azure)
                ),
                Cover(
                    RecordingGroupData.IC_COVER_PURE_GREY,
                    false,
                    root.findViewById(R.id.group_edit_cover_grey)
                ),
                Cover(
                    RecordingGroupData.IC_COVER_PURE_BROWN,
                    false,
                    root.findViewById(R.id.group_edit_cover_brown)
                )
            )
        )

        covers.forEach { cover ->
            cover.view?.setOnClickListener { _ -> updateSelectCover(cover) }
        }

        val curCoverId = groupInfo?.mGroupColor ?: RecordingGroupData.getDefaultPureCoverNum()
        updateSelectCover(covers.find {
            it.id == RecordingGroupData.groupColorInDb2PureCover(
                curCoverId
            )
        })
    }

    private data class Cover(val id: String, var select: Boolean, val view: View?)

    private val covers = mutableListOf<Cover>()

    private fun updateSelectCover(selectCover: Cover?) {
        covers.forEach { cover ->
            cover.select = cover.id == selectCover?.id

            val drawable = cover.view?.background as? LayerDrawable
            drawable?.findDrawableByLayerId(R.id.group_edit_cover_fg)?.alpha =
                if (cover.select) MAX_ALPHA_VALUE else 0
        }

        val id = GroupManageUtils.getResIdByResName(context, selectCover?.id)
        inputIcon?.setImageResource(id)
    }

    private fun initSingleInputView(root: View?) {
        if (root == null) {
            return
        }
        withIconCardSingleInputView = root.findViewById(R.id.group_edit_name)
        inputIcon = root.findViewById(R.id.input_icon)
        withIconCardSingleInputView?.apply {
            maxCount = MAX_GROUP_NAME_LENGTH
            isVerticalScrollBarEnabled = false
            editText?.addTextChangedListener(mTextWatcherProxy)
            editText?.setText(groupInfo?.mGroupName)
        }
        val headContainer = withIconCardSingleInputView?.findViewById<LinearLayout>(com.support.input.R.id.single_card)
        headContainer?.doOnLayout {
            it.background = null
            it.setBackgroundColor(Color.TRANSPARENT)
        }
    }

    private fun initToolbar() {
        toolbar.apply {
            visibility = View.VISIBLE
            title = if (isCreateMode()) {
                context.getString(com.soundrecorder.common.R.string.create_new_recording_group)
            } else {
                context.getString(com.soundrecorder.common.R.string.edit_group)
            }
            isTitleCenterStyle = true
            inflateMenu(R.menu.group_menu_edit)

            saveItem = menu.findItem(R.id.group_menu_edit_save)
            saveItem?.setOnMenuItemClickListener {
                triggerGroupSave()
                true
            }

            menu.findItem(R.id.group_menu_edit_cancel).setOnMenuItemClickListener {
                triggerGroupCancel()
                true
            }
        }
    }

    private fun triggerGroupCancel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    private var saveJob: Job? = null

    private fun triggerGroupSave() {
        if (saveJob == null || saveJob?.isCompleted == true) {
            saveJob = lifecycleScope.launch {
                val name = withIconCardSingleInputView?.editText?.text?.toString()?.trim() ?: ""
                val cover =
                    covers.find { it.select }?.id ?: RecordingGroupData.getDefaultPureCover()

                if (TextUtils.isEmpty(name)) {
                    withContext(Dispatchers.Main) {
                        ToastManager.showShortToast(
                            context,
                            com.soundrecorder.common.R.string.error_none_filename
                        )
                    }
                    return@launch
                }

                val result = groupEditViewModel.insertOrUpdateFromEdit(groupInfo, name, cover)
                if (result.second == GroupManageUtils.NUM_1_UPDATE_OR_INSERT_EXIST) { //新建或重命名，组名已存在。
                    ToastManager.showShortToast(
                        context,
                        com.soundrecorder.common.R.string.group_name_exists_new
                    )
                } else {
                    LocalEditingGroupChannel.notifyInsertGroupInfo(context, result.first)
                    triggerGroupCancel()
                }
            }
        }
    }

    private fun isCreateMode(): Boolean {
        return groupInfo == null
    }

    private fun initEditTextWatcher() {
        mTextWatcherProxy = CustomTextWatcherProxy(this)
    }

    private fun initTextFilter() {
        SubRecorderTextUtils.addIllgalFileNameInputFilter(withIconCardSingleInputView?.editText)
        SubRecorderTextUtils.addEmojiInputFilter(withIconCardSingleInputView?.editText)
    }

    private fun updateDeleteButtonAndLengthTips(hasFocus: Boolean) {
        val isLengthEmpty = TextUtils.isEmpty(
            withIconCardSingleInputView?.editText?.getText().toString()
        )
        withIconCardSingleInputView?.editText?.isFastDeletable = true
        withIconCardSingleInputView?.isEnableInputCount = !isLengthEmpty
        withIconCardSingleInputView?.maxCount = MAX_CHAR_LENGTH
    }

    private fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        renamePrompts?.visibility = View.GONE
        redLine?.visibility = View.GONE
        if (SubRecorderTextUtils.isContainEmoji()) {
            showTextNote(com.soundrecorder.common.R.string.notify_illegal_emoji_new)
        }
        if (SubRecorderTextUtils.isContainIllgalFileName()) {
            showTextNote(com.soundrecorder.common.R.string.error_strange_name)
        }
        val tmp = s.toString()
        val len = tmp.length
        if (len > MAX_CHAR_LENGTH) {
            if (count - before > 0) {
                withIconCardSingleInputView?.editText?.text?.delete(
                    MAX_CHAR_LENGTH,
                    start + count
                )
            }
            showTextNote(com.soundrecorder.common.R.string.namelength_outofrange)
        }

        saveItem?.isEnabled = !TextUtils.isEmpty(s)
    }

    private fun showTextNote(resId: Int) {
        renamePrompts?.setText(resId)
        renamePrompts?.visibility = View.VISIBLE
        redLine?.visibility = View.VISIBLE
        SubRecorderTextUtils.setContainEmoji(false)
        SubRecorderTextUtils.setContainIllgalFileName(false)
    }

    private fun initEditTalkBack() {
        val desText = StringBuffer()
        desText.append(activity?.getString(com.soundrecorder.common.R.string.recording_group_edit_tips))
        desText.append(withIconCardSingleInputView?.editText?.text)
        ((withIconCardSingleInputView?.editText) as? TalkBackEditTextView)?.setAccessibilityTouchHelper(
            desText.toString()
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        withIconCardSingleInputView?.editText?.removeTextChangedListener(mTextWatcherProxy)
        mTextWatcherProxy = null
    }

    override fun onDetach() {
        super.onDetach()
        hideInputKeyBoard()
    }

    private fun showInputKeyboard() {
        withIconCardSingleInputView?.editText?.let {
            it.removeCallbacks(runnable)
            it.postDelayed(runnable, SHOW_INPUT_DELAY)
        }
    }

    private fun hideInputKeyBoard() {
        withIconCardSingleInputView?.editText?.apply {
            removeCallbacks(runnable)
            activity?.let { hideSoftInput(it) }
        }
    }

    private class CustomTextWatcherProxy(groupEditFragment: GroupEditFragment) : TextWatcher {
        private var weakGroupEditFragment: WeakReference<GroupEditFragment> =
            WeakReference(groupEditFragment)

        override fun afterTextChanged(p0: Editable?) {
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            val fragment = weakGroupEditFragment.get() ?: return
            if (fragment.isDetached) return
            fragment.onTextChanged(s, start, before, count)
            fragment.updateDeleteButtonAndLengthTips(
                fragment.withIconCardSingleInputView?.hasFocus() ?: false
            )
        }
    }
}