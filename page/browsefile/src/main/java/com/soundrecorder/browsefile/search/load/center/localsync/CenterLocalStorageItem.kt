/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CenterLocalStorageItem
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.center.localsync

data class CenterLocalStorageItem(var mediaId: Long, var lastModify: Long) {

    companion object {
        private const val KEY_CONNECT_LINE = "-"

        fun convertToItem(originStr: String): CenterLocalStorageItem? {
            val items = originStr.split(KEY_CONNECT_LINE)
            return if (items.size >= 2) {
                CenterLocalStorageItem(items[0].toLongOrNull() ?: -1, items[1].toLongOrNull() ?: 0)
            } else {
                null
            }
        }
    }

    override fun toString(): String {
        return "$mediaId$KEY_CONNECT_LINE$lastModify"
    }
}