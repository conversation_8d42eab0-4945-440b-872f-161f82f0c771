/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CloudOffLineTipViewTest
 * Description:
 * Version: 1.0
 * Date: 2023/10/9
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/10/9 1.0 create
 */

package com.soundrecorder.browsefile.home.view.cloudtip

import android.os.Build
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.coui.appcompat.tips.def.COUIDefaultTopTips
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.browsefile.home.item.BrowseAdapter
import com.soundrecorder.browsefile.home.item.IBrowseViewHolderListener
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudGuideState
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class CloudGuideTipViewTest {
    private var activity: AppCompatActivity? = null
    private var adapter: BrowseAdapter? = null

    @Before
    fun setup() {
        activity = Robolectric.buildActivity(AppCompatActivity::class.java).get().apply {
            val listener = object : IBrowseViewHolderListener {
                override fun getWindowType(): MutableLiveData<WindowType> =
                    MutableLiveData<WindowType>()

                override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> =
                    MutableLiveData<StartPlayModel?>()
            }
            adapter = BrowseAdapter(this, this, listener)
        }
    }

    @After
    fun release() {
        activity = null
        adapter = null
    }

    @Test
    fun should_notNull_when_init() {
        val activity = activity ?: return

        val guideTipView = CloudGuideTipView(activity)

        val topTip = Whitebox.getInternalState<COUIDefaultTopTips>(guideTipView, "tipView")
        Assert.assertNotNull(topTip)
    }

    @Test
    fun should_correct_when_updateSPValueWhenClickIgnore() {
        val activity = activity ?: return
        PrefUtil.putInt(activity, PrefUtil.KEY_GUIDE_STATE, CloudGuideState.GUIDE_STATE_DEFAULT)

        val guideTipView = CloudGuideTipView(activity)
        Whitebox.invokeMethod<Unit>(guideTipView, "updateSPValueWhenClickIgnore")
        var result =
            PrefUtil.getInt(activity, PrefUtil.KEY_GUIDE_STATE, CloudGuideState.GUIDE_STATE_DEFAULT)
        Assert.assertEquals(CloudGuideState.GUIDE_STATE_IGNORE, result)

        Whitebox.invokeMethod<Unit>(guideTipView, "updateSPValueWhenClickIgnore")
        result =
            PrefUtil.getInt(activity, PrefUtil.KEY_GUIDE_STATE, CloudGuideState.GUIDE_STATE_DEFAULT)
        Assert.assertEquals(CloudGuideState.GUIDE_STATE_IGNORE_AGAIN, result)
    }

    @Test
    fun should_correct_when_onIgnoreButtonClick() {
        val activity = activity ?: return
        val adapter = adapter ?: return
        val mockStaticCloudUtil = Mockito.mockStatic(CloudStaticsUtil::class.java)

        val guideTipView = CloudGuideTipView(activity)
        Whitebox.invokeMethod<Unit>(guideTipView, "onIgnoreButtonClick")
        mockStaticCloudUtil.verify { CloudStaticsUtil.addCloudTipsClickIgnoreEvent() }
    }
}