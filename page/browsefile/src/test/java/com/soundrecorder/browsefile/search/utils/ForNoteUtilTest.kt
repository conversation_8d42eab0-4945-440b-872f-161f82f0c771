/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ForNoteUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.utils

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel
import com.soundrecorder.browsefile.shadows.ShadowCenterDbUtils
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.db.NoteDbUtils
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowCenterDbUtils::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class ForNoteUtilTest {

    private var mockedStatic: MockedStatic<NoteDbUtils>? = null

    @Before
    fun init() {
        mockedStatic = Mockito.mockStatic(NoteDbUtils::class.java)
    }

    @After
    fun tearDown() {
        mockedStatic?.reset()
        mockedStatic = null
    }

    @Test
    fun should_equals_when_mergeSummaryAndNeutronList() {
        val summaryList = mutableListOf<ItemSearchViewModel>()
        val neutronList = mutableListOf<ItemSearchViewModel>()
        var result = ForNoteUtil.mergeSummaryAndNeutronList(summaryList, neutronList)
        Assert.assertTrue(result.isEmpty())

        for (i in 1..5) {
            mockedStatic?.`when`<NoteData> { NoteDbUtils.queryNoteByMediaId(i.toString()) }
                ?.thenReturn(NoteData(i.toString(), 0, "$i$i$i", "", i.toString(), mediaPath = ""))
            summaryList.add(ItemSearchViewModel().also {
                it.summaryText = "录音摘要生成文本，这个是自己写的测试用例$i"
                it.contentColorIndex = ArrayList<Int>().apply {
                    add(0)
                    add(1)
                }
                it.mediaId = i.toLong()
                it.noteId = "$i$i$i"
            })
        }
        result = ForNoteUtil.mergeSummaryAndNeutronList(summaryList, neutronList)
        Assert.assertEquals(5, result.size)

        val neutronId = mutableListOf<Int>(1, 3, 7, 8, 9, 10)
        for (id in neutronId) {
            neutronList.add(ItemSearchViewModel().also {
                it.mediaId = id.toLong()
                it.noteId = if (id == 1 || id == 3) "$id$id$id" else null
            })
        }
        result = ForNoteUtil.mergeSummaryAndNeutronList(summaryList, neutronList)
        Assert.assertEquals(9, result.size)
        Assert.assertEquals(3, result[1].mediaId)
    }
}