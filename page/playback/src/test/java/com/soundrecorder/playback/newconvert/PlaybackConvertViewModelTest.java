/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/01/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.playback.newconvert;

import static org.mockito.ArgumentMatchers.anyInt;

import android.app.Application;
import android.content.Context;
import android.os.Build;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.playback.convert.search.ConvertSearchBean;
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.List;

import kotlin.Pair;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class PlaybackConvertViewModelTest {

    private Context mContext;
    private Application mApplication;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mApplication = BaseApplication.getApplication();
    }

    @After
    public void tearDown() {
        mContext = null;
        mApplication = null;
    }

    @Test
    public void verify_value_when_saveScrollPosition() {
        PlaybackConvertViewModel model = new PlaybackConvertViewModel(mApplication);
        LinearLayoutManager manager = new LinearLayoutManager(mContext);
        model.saveScrollPosition("ceshi", manager);
        model.restoreScrollPosition(manager);
        boolean isScreen = model.isHeaderInScreen();
        Assert.assertTrue(!isScreen);
        model.swithSpeakerRoleState();
    }

    @Test
    public void verify_value_when_rectifyStartSeqForTextMetaDataItem() {
        PlaybackConvertViewModel model = new PlaybackConvertViewModel(mApplication);
        List<Pair<Integer, ConvertContentItem.TextItemMetaData>> pairs = new ArrayList<>();
        ConvertContentItem.TextItemMetaData itemMetaData = new ConvertContentItem.TextItemMetaData();
        List<ConvertContentItem.SubSentence> subSentences = new ArrayList<>();
        ConvertContentItem.SubSentence sentence = new ConvertContentItem.SubSentence(
                0, 10, 0, "我来测试一下转文本哈哈", true, true, null);
        subSentences.add(sentence);
        itemMetaData.setTextParagraph(subSentences);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair = new Pair<>(0, itemMetaData);
        pairs.add(pair);
        model.setMTextItems(pairs);
        ConvertSearchBean bean = new ConvertSearchBean("我", 0, 0, true);
        Pair<String, Integer> pairResult = model.rectifyStartSeqForTextMetaDataItem(bean);
        Assert.assertNotNull(pairResult);
    }

    @Test
    public void verify_value_when_executeAsyncCheckCompletedConvert() {
        PlaybackConvertViewModel model = new PlaybackConvertViewModel(mApplication);
        model.setMediaRecordId(10);
        model.executeAsyncCheckCompletedConvert();
    }

    @Test
    public void verify_value_when_staticsEvent() throws Exception {
        MockedStatic<ConvertStaticsUtil> staticsUtilMockedStatic = Mockito.mockStatic(ConvertStaticsUtil.class);
        PlaybackConvertViewModel model = new PlaybackConvertViewModel(mApplication);
        Whitebox.invokeMethod(model, "staticsEvent");
        staticsUtilMockedStatic.verify(() -> ConvertStaticsUtil.addInConvertSearchEvent(anyInt()), Mockito.times(0));
        staticsUtilMockedStatic.verify(() -> ConvertStaticsUtil.clickKeyWordChipEvent(anyInt()), Mockito.times(0));

        model.setClickChipCount(10);
        model.setClickConvertSearchCount(10);
        Whitebox.invokeMethod(model, "staticsEvent");
        staticsUtilMockedStatic.verify(() -> ConvertStaticsUtil.addInConvertSearchEvent(anyInt()), Mockito.times(1));
        staticsUtilMockedStatic.verify(() -> ConvertStaticsUtil.clickKeyWordChipEvent(anyInt()), Mockito.times(1));

        staticsUtilMockedStatic.close();
    }
}
