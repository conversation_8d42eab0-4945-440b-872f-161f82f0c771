/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MarkListContainerFragment.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/02
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import android.annotation.SuppressLint
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.toColorInt
import androidx.core.view.isVisible
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.common.utils.ViewUtils.addItemDecorationBottom
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.playback.R
import com.soundrecorder.wavemark.mark.MarkListAdapter

class MarkListContainerFragment(val fragmentMarkListAdapter: MarkListAdapter? = null)  : COUIPanelFragment(),
    ViewTreeObserver.OnGlobalLayoutListener {
    companion object {
        private const val TAG = "MarkListContainerFragment"
        private const val DARK_BG_COLOR = "#FF333333"
        private const val BG_COLOR = "#FFF0F1F2"
    }

    private var markListRecyclerView: COUIRecyclerView? = null
    private var markListEmptyImageView: OSImageView? = null
    private var markListEmptyContainer: ConstraintLayout? = null
    private var markListContentView: View? = null
    private var toolBarView: View? = null

    @SuppressLint("InflateParams")
    override fun initView(panelView: View?) {
        toolbar?.visibility = View.GONE
        LayoutInflater.from(activity).inflate(R.layout.fragment_playback_mark_list, null, false).apply {
            findViewById<com.coui.appcompat.toolbar.COUIToolbar>(R.id.mark_list_toolbar)?.apply {
                title = context?.resources?.getString(com.soundrecorder.base.R.string.mark_list)
                isTitleCenterStyle = true
                inflateMenu(R.menu.mark_list_menu)
                menu?.findItem(R.id.cancel).apply {
                    setOnMenuItemClickListener {
                        dismissPanel()
                        setOnMenuItemClickListener(null)
                        true
                    }
                }

                toolbar?.menuView?.setOverflowMenuListener {
                    it.setOnItemClickListener { _, _, _, _ -> it.dismiss() }
                }
            }

            markListRecyclerView = this.findViewById(R.id.mark_list_recycler_view)
            if (markListRecyclerView == null) {
                DebugUtil.i(TAG, "initView <<markListRecyclerView is null.")
            }

            markListEmptyImageView = this.findViewById(R.id.iv_mark_list_empty_image)
            if (markListEmptyImageView == null) {
                DebugUtil.i(TAG, "<<markListEmptyImageView is null.")
            }

            markListEmptyContainer = this.findViewById(R.id.iv_mark_list_empty_view)
            if (markListEmptyContainer == null) {
                DebugUtil.i(TAG, "initView <<markListEmptyContainer is null.")
            }


            toolBarView = this.findViewById(R.id.mark_list_toolbar)
            if (toolBarView == null) {
                DebugUtil.i(TAG, "initView <<toolBarView is null.")
            }
            (contentView as? ViewGroup)?.addView(this)

            markListContentView = contentView
        }

        markListRecyclerView?.let {
            it.layoutManager = LinearLayoutManager(it.context)
            it.adapter = fragmentMarkListAdapter
            it.addItemDecorationBottom(com.soundrecorder.common.R.dimen.card_margin_top_buttom)
        }

        markListEmptyImageView?.initImageResource()

        setDialogContentViewState()
        /*
         * Since 15.0, the dragView of panel without handle is hidden by default,
         * and there is no need to actively call this method.
         */
        //hideDragView()
    }

    fun setDialogContentViewState() {
        var isShowRecyclerView = true
        if (fragmentMarkListAdapter == null || fragmentMarkListAdapter.itemCount == 0) {
            isShowRecyclerView = false
        }
        DebugUtil.i(TAG, "setDialogContentViewState isShowRecyclerView:$isShowRecyclerView"
        )

        DebugUtil.i(TAG, "setDialogContentViewState item count:${fragmentMarkListAdapter?.itemCount}"
        )
        markListEmptyContainer?.isVisible = (!isShowRecyclerView)
        markListEmptyImageView?.isVisible = (!isShowRecyclerView)
        markListRecyclerView?.isVisible = isShowRecyclerView
        setContentViewHeightListener()
    }

    private fun setContentViewHeightListener() {
        if (markListEmptyContainer?.isVisible == true) {
            markListEmptyContainer?.viewTreeObserver?.addOnGlobalLayoutListener(this)
        } else {
            markListEmptyContainer?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
        }
    }

    private fun dismissPanel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    private fun initBackgroundColor() {
        var colorValue = context?.getColor(R.color.mark_list_dialog_color)

        DebugUtil.i(TAG, "initBackgroundColor Resource color value:$colorValue constant color value:${BG_COLOR.toColorInt()}")

        if (colorValue == null) {
            colorValue = NightModeUtil.isNightMode(context).let {
                if (it) DARK_BG_COLOR.toColorInt() else BG_COLOR.toColorInt()
            }
        }

        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
            ?.setPanelBackgroundTintColor(colorValue)
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        initBackgroundColor()
    }

    override fun onGlobalLayout() {
        if (isAdded.not()) {
            return
        }
        val bottomHeight: Int = ScreenUtil.getRealBounds(requireActivity()).bottom
        val toolBarVisible = Rect()
        val bottomMargin: Float = if (isFullScreenDisplay()) {
            BaseApplication.getAppContext().resources.getDimension(R.dimen.full_screen_empty_mark_list_margin_bottom)
        } else {
            BaseApplication.getAppContext().resources.getDimension(R.dimen.half_screen_empty_mark_list_margin_bottom)
        }

        var emptySpace = bottomHeight - bottomMargin.toInt()
        if (toolBarView != null) {
            toolBarView?.getGlobalVisibleRect(toolBarVisible)
            emptySpace -= toolBarVisible.bottom
        }

        if (emptySpace < 0) {
            emptySpace = 0
        }

        markListEmptyContainer?.layoutParams?.apply {
            height = emptySpace
        }?.let {
            markListEmptyContainer?.layoutParams = it
        }

        var emptyListImageHeight = 0.0f
        var emptyListImageWidth = 0.0f
        if (isFullScreenDisplay()) {
            emptyListImageHeight = BaseApplication.getAppContext().resources.getDimension(R.dimen.full_screen_empty_mark_list_height)
            emptyListImageWidth = BaseApplication.getAppContext().resources.getDimension(R.dimen.full_screen_empty_mark_list_width)
        } else {
            emptyListImageHeight = BaseApplication.getAppContext().resources.getDimension(R.dimen.half_screen_empty_mark_list_height)
            emptyListImageWidth = BaseApplication.getAppContext().resources.getDimension(R.dimen.half_screen_empty_mark_list_width)
        }

        markListEmptyImageView?.layoutParams?.apply {
            height = emptyListImageHeight.toInt()
            width = emptyListImageWidth.toInt()
        }?.let {
            markListEmptyImageView?.layoutParams = it
        }

        DebugUtil.i(TAG, "onGlobalLayout bottomHeight is $bottomHeight emptySpace is $emptySpace.")
    }

    private fun isFullScreenDisplay(): Boolean {
        if (markListContentView == null) {
            return false
        }

        val visibleRect = Rect()
        markListContentView?.getGlobalVisibleRect(visibleRect)
        val visibleHeight = visibleRect.bottom - visibleRect.top

        DebugUtil.i(TAG, "isFullScreenDisplay bottomHeight is $visibleHeight height is ${markListContentView?.height}.")
        return visibleHeight == markListContentView?.height
    }
}
