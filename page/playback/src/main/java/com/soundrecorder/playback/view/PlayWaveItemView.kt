/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayWaveItemView
 * Description:
 * Version: 1.0
 * Date: 2023/6/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/26 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.RectF
import android.util.AttributeSet
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.WaveViewUtil.getOneWaveLineTimeByWaveType
import com.soundrecorder.wavemark.wave.view.WaveItemView
import com.soundrecorder.wavemark.wave.view.drawNormalPlayAmp

class PlayWaveItemView(context: Context, attrs: AttributeSet?, defStyle: Int) : WaveItemView(context, attrs, defStyle) {

    companion object {
        private const val TAG = "PlayWaveItemView"
        private const val FAKE_AMPLITUDE: Int = 500
    }

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    override fun drawDottedLineWhenNoData(): Boolean {
        return true
    }

    data class DrawPlayAmplitudeParams(
        var currentX: Float = 0f,
        var startX: Float = 0f,
        var amplitueIndex: Int = 0,
        var amplitueVaule: Int = 0,
        var preAmplitueVaule: Int = 0,
        var waveStartY: Int = 0,
        var waveEndY: Int = 0,
        var lineHeight: Float = 0f,
        val isRTL:Boolean,
        var isInDirectRecordTime: Boolean = false,
        var startTime: Long = -1L,
        var endTime: Long = -1L,
        var waveStartTime: Long = -1L,
        var ampValue: Int? = 0
    )

    override fun drawPlayAmplitude(canvas: Canvas) {
        val params = DrawPlayAmplitudeParams(isRTL = isReverseLayout)

        //波形实际上是画从第二个item开始绘制（index = 1）
        params.amplitueIndex = calculateAmpStartIndex()
        // startX is start draw postion on x orientation of the first item in mRecordAmplitudeList or mPlayAmplitudes.
        params.startX = calculateAmpStartX(params.amplitueIndex.toLong())
        params.currentX = params.startX
        params.waveStartTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION
        //清理已缓存的波形音柱开始绘制的位置
        cachedAmpStartXList.clear()

        val viewWidth = width
        while (params.currentX <= viewWidth) {
            when {
                (amplitudes != null && amplitudes.size > 0) -> {
                    if (params.amplitueIndex < amplitudes.size) {
                        calculateFromAmplitudes(params)
                    } else {
                        drawDottedLine(canvas, params.isRTL, params.currentX, viewWidth)
                        params.currentX += mVirtualAmpGap
                        continue
                    }
                }
            }

            if ((amplitudes != null) && (amplitudes.size > 0)) { // the amplitude is from mp3 book mark
                if (params.amplitueIndex < amplitudes.size) {
                    calculateFromAmplitudes(params)
                } else {
                    drawDottedLine(canvas, params.isRTL, params.currentX, viewWidth)
                    params.currentX += mVirtualAmpGap
                    continue
                }
            } else { // the amplitude is from decoding sound files
                if ((decodedAmplitudeList != null) && (decodedAmplitudeList.size > 0)) {
                    if (params.amplitueIndex < decodedAmplitudeList.size) {
                        calculateFromDecodedAmplitudeList(params)
                    } else {
                        val usedFakeAmplitude = calculateFakeAmplitude(params)
                        if (!usedFakeAmplitude) {
                            drawDottedLine(canvas, params.isRTL, params.currentX, viewWidth)
                            params.currentX += mVirtualAmpGap
                            continue
                        }
                    }
                } else {
                    if (!drawDottedLineWhenNoData()) {
                        DebugUtil.d(TAG, "---drawAmplitude no waveform data---")
                        return
                    }
                    params.waveStartY = getStartYByHeight(mDottedLineHeight)
                    params.waveEndY = getEndYByHeight(mDottedLineHeight)
                    canvas.drawLine(params.currentX, params.waveStartY.toFloat(), params.currentX, params.waveEndY.toFloat(), mHorizontalDashPaint)
                    params.currentX += mVirtualAmpGap
                    continue
                }
            }
            //步骤二：计算波形的上下左右位置的坐标，准备绘制波形矩形
            params.lineHeight = getWaveLineHeight(params.preAmplitueVaule, params.amplitueVaule)
            //步骤三、步骤四
            val nextCurrentX = this.drawNormalPlayAmp(canvas, params.currentX, params.lineHeight)
            // 绘制定向录音音柱
            drawEnhanceAmplitude(params.waveStartTime, params, canvas)
            params.currentX = nextCurrentX
        }
    }

    private fun calculateFakeAmplitude(params: DrawPlayAmplitudeParams): Boolean {
        var usedFakeAmplitude = false
        val oneWaveLineTime = getOneWaveLineTimeByWaveType(context, mWaveType)
        val decodeIsBeforeTotal = oneWaveLineTime * decodedAmplitudeList.size < mTotalTime
        val nowIsBeforeTotal = oneWaveLineTime * params.amplitueIndex < mTotalTime
        if (decodeIsBeforeTotal && nowIsBeforeTotal) {
            //In this case, we temporary use fake amplitude, for bug 1646242
            DebugUtil.d(
                TAG, ("In this case, we temporary use fake amplitude, amp : "
                        + ", mPlayTotalTime: " + mTotalTime + ", decodedAmplitudeList size : "
                        + decodedAmplitudeList.size)
            )
            usedFakeAmplitude = true
            params.amplitueVaule = (FAKE_AMPLITUDE * Math.random()).toInt()
            params.preAmplitueVaule = (FAKE_AMPLITUDE * Math.random()).toInt()
            if (mViewIndex == mTotalCount - 1) {
                params.amplitueIndex++
            }
        }
        return usedFakeAmplitude
    }

    private fun calculateFromDecodedAmplitudeList(params: DrawPlayAmplitudeParams) {
        if (params.amplitueIndex - 1 >= 0) {
            params.ampValue = decodedAmplitudeList[params.amplitueIndex - 1]
            params.preAmplitueVaule = params.ampValue ?: 0
        } else {
            params.preAmplitueVaule = 0
        }
        params.ampValue = decodedAmplitudeList[params.amplitueIndex]
        params.amplitueVaule = params.ampValue ?: 0
        params.amplitueIndex++
    }

    private fun calculateFromAmplitudes(params: DrawPlayAmplitudeParams) {
        if (params.amplitueIndex - 1 >= 0) {
            params.ampValue = amplitudes[params.amplitueIndex - 1]
            params.preAmplitueVaule = params.ampValue ?: 0
        } else {
            params.preAmplitueVaule = 0
        }
        params.ampValue = amplitudes[params.amplitueIndex]
        params.amplitueVaule = params.ampValue ?: 0
        params.amplitueIndex++
    }

    /**
     * 绘制定向录音音柱
     * @param waveStartTime 当前波形的起始时间
     * @param params 当前绘制的参数
     * @param canvas 画布对象
     */
    private fun drawEnhanceAmplitude(
        waveStartTime: Long,
        params: DrawPlayAmplitudeParams,
        canvas: Canvas
    ) {
        if (isEnhance && directTimeList?.isNotEmpty() == true) {
            val currentTime = waveStartTime + ((params.currentX - params.startX) / pxPerMs).toLong()
            val isNeedInit = (!params.isInDirectRecordTime && params.startTime == -1L)
            val isNeedUpdate =
                (!params.isInDirectRecordTime && currentTime >= params.startTime) || (params.isInDirectRecordTime && currentTime > params.endTime)
            if (isNeedInit || isNeedUpdate) {
                DebugUtil.d(TAG, "drawPlayAmplitude isNeedInit=$isNeedInit, isNeedUpdate=$isNeedUpdate currentTime=$currentTime" +
                            ", startTime=${params.startTime}, endTime=${params.endTime}")
                val isInDirectRecordTimeRange = isInDirectRecordTimeRange(currentTime)
                params.isInDirectRecordTime = isInDirectRecordTimeRange.first
                params.startTime = isInDirectRecordTimeRange.second
                params.endTime = isInDirectRecordTimeRange.third
                DebugUtil.i(TAG, "drawPlayAmplitude isInDirectRecordTime=${params.isInDirectRecordTime}" +
                        ", startTime=${params.startTime}, endTime=${params.endTime}")
            }
            if (params.isInDirectRecordTime && currentTime in params.startTime..params.endTime) {
                drawEnhanceAmpColumn(canvas, params.currentX, params.lineHeight)
            }
        }
    }

    /**
     * 获取指定时间点是否在定向录音范围
     * @param time 要检查的时间点
     * @return Triple<Boolean, Long, Long> 第一个元素表示是否在定向录音时间段内
     * 若第一个元素为 true，则第二个和第三个元素表示定向录音时间段的起始和结束时间
     * 若第一个元素为 false，则第二个和第三个元素表示当前时间点的后面一段定向录音时间段的起始和结束时间
     */
    private fun isInDirectRecordTimeRange(time: Long): Triple<Boolean, Long, Long> {
        // 若当前时间在定向录音时间段内，则返回true，并返回定向录音时间段
        directTimeList?.forEach { directTime ->
            if (time >= directTime.startTime && time <= directTime.endTime) {
                return Triple(true, directTime.startTime, directTime.endTime)
            }
        }
        // 计算当前时间点的后面一段定向录音时间段
        directTimeList?.forEach { directTime ->
            if (time < directTime.startTime) {
                return Triple(false, directTime.startTime, directTime.endTime)
            }
        }
        // 当前时间点后面没有定向录音时间段，则返回false，并返回 0，0
        return Triple(false, 0, 0)
    }

    /**
     * 绘制单个定向录音音柱
     * @param canvas 画布对象
     * @param currentX 音柱的X坐标位置
     * @param lineHeight 音柱的高度
     */
    private fun drawEnhanceAmpColumn(canvas: Canvas, currentX: Float, lineHeight: Float) {
        val waveStartY = getStartYByHeight(lineHeight)
        val waveEndY = getEndYByHeight(lineHeight)
        var newCurrentX = currentX
        var newCurrentXLatter = currentX + mDirectAmpWidth
        // 处理RTL布局
        if (isReverseLayout) {
            newCurrentX = width - currentX
            newCurrentXLatter = width - currentX - mDirectAmpWidth
        }

        // 使用定向录音专用画笔绘制音柱
        mEnhanceAmplitudePaint?.let { paint ->
            val ampRectF = RectF(newCurrentX, waveStartY.toFloat(), newCurrentXLatter, waveEndY.toFloat())
            canvas.drawRoundRect(ampRectF, mAmplitudeWidth, mAmplitudeWidth, paint)
        }
    }
}