/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonFlavor
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.recorderlog.util

import android.os.Build
import com.oplus.recorderlog.log.RecorderLogger

class CommonFlavor {

    companion object {
        const val TAG = "CommonFlavor"
        const val PACKAGE_NAME_ONEPLUS_EXPORT = "com.oneplus.soundrecorder"
        const val PACKAGE_NAME_NORMAL = "com.coloros.soundrecorder"

        const val BRAND_REALME = "realme"
        const val BRAND_ONEPLUS = "oneplus"
        const val BRAND_OPPO = "oppo"
        const val DOMESTIC = "domestic"

        private var sInstance: CommonFlavor? = null

        @JvmStatic
        @Suppress("UnsafeCallOnNullableType")
        fun getInstance(): CommonFlavor {
            if (sInstance == null) {
                synchronized(CommonFlavor::class) {
                    if (sInstance == null) {
                        sInstance = CommonFlavor()
                    }
                }
            }

            return sInstance!!
        }
    }

    private var debug: Boolean? = null
    var flavor: String? = null
        private set
    var flavorB: String? = null
        private set
    var flavorP: String? = null
        private set
    var flavorRegion: String? = null
        private set
    var flavorApiLevel: String? = null
        private set

    private var packageName: String? = null

    fun init(
        debug: Boolean?,
        flavor: String?,
        flavorB: String?,
        flavorP: String?,
        flavorRegion: String?,
        flavorApiLevel: String?
    ) {
        this.debug = debug
        this.flavor = flavor
        this.flavorB = flavorB
        this.flavorP = flavorP
        this.flavorRegion = flavorRegion
        this.flavorApiLevel = flavorApiLevel
    }

    fun getPackageName(): String {
        if (packageName.isNullOrEmpty()) {
            val flavorInLowerCase = flavor?.lowercase() ?: ""
            packageName = if (flavorInLowerCase.contains("oneplus")
                && (flavorInLowerCase.contains("export")
                        || flavorInLowerCase.contains("gdpr"))
            ) {
                PACKAGE_NAME_ONEPLUS_EXPORT
            } else {
                PACKAGE_NAME_NORMAL
            }
        }

        return packageName ?: PACKAGE_NAME_NORMAL
    }

    fun isOnePlus(): Boolean {
        RecorderLogger.i(
            TAG,
            "isOnePlus: brand = ${Build.BRAND}", false
        )
        return BRAND_ONEPLUS.equals(Build.BRAND, true)
    }

    fun isRealme(): Boolean {
        RecorderLogger.i(
            TAG,
            "isRealme: brand = ${Build.BRAND}", false
        )
        return BRAND_REALME.equals(Build.BRAND, true)
    }

    fun isOPPO(): Boolean {
        RecorderLogger.i(
            TAG,
            "isOPPO: brand = ${Build.BRAND}", false
        )
        return BRAND_OPPO.equals(Build.BRAND, true)
    }

    fun hasExpFeature(): Boolean = flavorRegion?.equals(DOMESTIC, true) == false
}