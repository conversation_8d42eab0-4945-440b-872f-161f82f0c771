package io.noties.markwon.core.factory

import io.noties.markwon.MarkwonConfiguration
import io.noties.markwon.RenderProps
import io.noties.markwon.SpanFactory
import io.noties.markwon.core.spans.CustomParagraphSpan
import io.noties.markwon.prop.SpaceProps
import io.noties.markwon.prop.SpaceProps.Companion.LIST_ITEM_TOP_SPACE

class ParagraphSpanFactory: SpanFactory {

    override fun getSpans(configuration: MarkwonConfiguration, props: RenderProps): Any? {
        val spaceLevel = LIST_ITEM_TOP_SPACE.get(props, SpaceProps.LEVEL_NO_SPACE)
        return CustomParagraphSpan(configuration.theme(), spaceLevel)
    }
}