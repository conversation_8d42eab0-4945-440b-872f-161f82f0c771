package com.soundrecorder.common.buryingpoint;

import android.text.TextUtils;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.DebugUtil;

import java.util.HashMap;


public class CloudStaticsUtil {

    public static final String TAG = "CloudStaticsUtil";
    public static final String EVENT_CLOUD_LOG = "cloud_log";
    public static final String KEY_CLOUD_LOG_MESSAGE = "message";


    /**
     *  统计云服务顶部提示曝光次数
     */
    public static void addCloudTipsCardPopEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(RecorderUserAction.KEY_CLOUD_TIPS_POP_NUM, RecorderUserAction.DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CLOUD,
                RecorderUserAction.EVENT_CLOUD_TIPS_CARD,
                eventInfo, false);
        DebugUtil.i(TAG, "addCloudTipsCardPopEvent");
    }

    /**
     * 统计云服务顶部提示点击开启的次数
     */
    public static void addCloudTipsClickOpenEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(RecorderUserAction.KEY_CLOUD_TIPS_CLICK_OPEN_NUM, RecorderUserAction.DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CLOUD,
                RecorderUserAction.EVENT_CLOUD_TIPS_CARD,
                eventInfo, false);
        DebugUtil.i(TAG, "addCloudTipsClickOpenEvent");
    }

    /**
     * 统计云服务顶部提示点击忽略的次数
     */
    public static void addCloudTipsClickIgnoreEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(RecorderUserAction.KEY_CLOUD_TIPS_CLICK_IGNORE_NUM, RecorderUserAction.DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CLOUD,
                RecorderUserAction.EVENT_CLOUD_TIPS_CARD,
                eventInfo, false);
        DebugUtil.i(TAG, "addCloudTipsClickIgnoreEvent");
    }

    /**
     *
     *   @param configId    id
     *   @param clickString button content
     *   @param clickType   0/1 = left/right button
     *   @param content 引导弹窗内容
     */
    public static void addCloudConfigClickEvent(String configId, String clickString, String clickType, String content) {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(RecorderUserAction.CLICK_EVENT, "");
        if (TextUtils.isEmpty(configId)) {
            eventInfo.put(RecorderUserAction.KEY_CLOUD_OPERATION_ID, "");
        } else {
            eventInfo.put(RecorderUserAction.KEY_CLOUD_OPERATION_ID, configId);
        }
        eventInfo.put(RecorderUserAction.KEY_CLOUD_OPERATION_BUTTON_CONTENT, clickString);
        eventInfo.put(RecorderUserAction.KEY_CLOUD_OPERATION_BUTTON_TYPE, clickType);
        eventInfo.put(RecorderUserAction.KEY_CLOUD_OPERATION_CONTENT, content);

        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CLOUD,
                RecorderUserAction.EVENT_CLOUD_TIPS_CARD,
                eventInfo, false);
        DebugUtil.i(TAG, "addCloudConfigClickIgnoreEvent");
    }

    /**
     *
     * @param configId 后台文案id
     * @param content  配置文案
     */
    public static void cloudConfigExposureEvent(String configId, String content) {
        HashMap<String, String> map = new HashMap<>();
        map.put(RecorderUserAction.EXPOSURE_EVENT, "");
        if (TextUtils.isEmpty(configId)) {
            map.put(RecorderUserAction.KEY_CLOUD_OPERATION_ID, "");
        } else {
            map.put(RecorderUserAction.KEY_CLOUD_OPERATION_ID, configId);
        }

        map.put(RecorderUserAction.KEY_CLOUD_OPERATION_CONTENT, content);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CLOUD,
                RecorderUserAction.EVENT_CLOUD_TIPS_CARD,
                map, false);
        DebugUtil.i(TAG, "cloudConfigExposureEvent");
    }

    /**
     * 统计点击查看云端数据的次数
     */
    public static void addCloudTipsClickViewDataEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(RecorderUserAction.KEY_CLOUD_TIPS_CLICK_VIEW_DATA_NUM, RecorderUserAction.DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CLOUD,
                RecorderUserAction.EVENT_CLOUD_TIPS_CARD,
                eventInfo, false);
        DebugUtil.i(TAG, "addCloudTipsClickViewDataEvent");
    }

    /**
     * 统计云服务升级引导文案曝光次数
     */
    public static void addCloudTipsUpgradeSpacePopEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(RecorderUserAction.KEY_CLOUD_TIPS_UPGRADE_SPACE_POP_NUM, RecorderUserAction.DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CLOUD,
                RecorderUserAction.EVENT_CLOUD_TIPS_CARD,
                eventInfo, false);
        DebugUtil.i(TAG, "addCloudTipsUpgradeSpacePopEvent");
    }

    /**
     * 统计云服务升级引导文案点击次数
     */
    public static void addCloudTipsClickUpgradeSpaceEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(RecorderUserAction.KEY_CLOUD_TIPS_UPGRADE_SPACE_CLICK_NUM, RecorderUserAction.DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CLOUD,
                RecorderUserAction.EVENT_CLOUD_TIPS_CARD,
                eventInfo, false);
        DebugUtil.i(TAG, "addCloudTipsClickUpgradeSpaceEvent");
    }

    /**
     * 云同步相关联日志信息
     * @param tag
     * @param message
     */
    public static void addCloudLog(String tag, String message) {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_CLOUD_LOG_MESSAGE, "[" + tag + "] " + message);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CLOUD,
                EVENT_CLOUD_LOG,
                eventInfo, false);
        DebugUtil.d(TAG, "addCloudLog");
    }
}
