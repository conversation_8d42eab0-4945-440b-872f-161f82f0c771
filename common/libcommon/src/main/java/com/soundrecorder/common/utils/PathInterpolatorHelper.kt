/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PathInterpolatorHelper
 * Description:
 * Version: 1.0
 * Date: 2023/11/15
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/11/15 1.0 create
 */

package com.soundrecorder.common.utils

import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator

object PathInterpolatorHelper {
    /*0.33F, 0.0F, 0.67F, 1.0F*/
    val couiEaseInterpolator = COUIEaseInterpolator()

    /*0.3F, 0.0F, 0.1F, 1.0F*/
    val couiMoveEaseInterpolator = COUIMoveEaseInterpolator()
}