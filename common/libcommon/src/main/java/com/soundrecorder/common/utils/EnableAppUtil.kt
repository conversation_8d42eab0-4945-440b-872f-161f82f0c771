package com.soundrecorder.common.utils

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.PackageManager.NameNotFoundException
import android.net.Uri
import android.provider.Settings
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R

object EnableAppUtil {
    /*应用未安装*/
    const val APP_NOT_INSTALLED = 1
    /*应用安装但被禁用*/
    const val APP_IS_DISABLED = 2
    /*应用安装且可用*/
    const val APP_IS_ENABLE = 3

    private const val TAG = "EnableAppUtil"

    /**
     * Only used to determine whether the APP is disabled, not disabled by default
     */
    @JvmStatic
    fun isAppEnabled(context: Context?, packageName: String): Boolean = isAppInstallEnabled(context, packageName) != APP_IS_DISABLED

    /**
     * @return APP_NOT_INSTALLED 未安装该应用
     * @return APP_IS_DISABLED  已安装应用且被禁用
     * @return APP_IS_ENABLE  已安装应用且可用
     */

    @JvmStatic
    fun isAppInstallEnabled(context: Context?, packageName: String): Int {
        val mPm: PackageManager? = context?.packageManager
        try {
            val mApplicationInfo = mPm?.getApplicationInfo(packageName, PackageManager.MATCH_DISABLED_COMPONENTS)
            mApplicationInfo?.let {
                return if (!it.enabled) {
                    DebugUtil.d(TAG, "isAppInstallEnabled packageName=$packageName is disabled")
                    APP_IS_DISABLED
                } else {
                    DebugUtil.d(TAG, "isAppInstallEnabled packageName=$packageName is enabled")
                    APP_IS_ENABLE
                }
            }
        } catch (e: NameNotFoundException) {
            DebugUtil.e(TAG, "isAppInstallEnabled packageName=$packageName is not exist. e = $e")
        }
        return APP_NOT_INSTALLED
    }


    @JvmStatic
    fun showEnableDialog(
        context: Context,
        packageName: String,
        @StringRes title: Int,
        @StringRes content: Int,
        cancelListener: (() -> Unit)? = null
    ): AlertDialog {
        val builder = COUIAlertDialogBuilder(context)
        builder.setBlurBackgroundDrawable(true)
        builder.setCancelable(false)
        val name = AppUtil.getAppName(packageName)
        builder.setTitle(context.getString(title, name))
        builder.setMessage(context.getString(content, name))
        val acceptClickListener = DialogInterface.OnClickListener { dlg, _ ->
            try {
                val intent = Intent()
                //BugId:2462075 -->【20161S】【录音】【OS12.1】【图片标记】【必现】录制界面，弹出是否启用相机弹窗，点击启用，进入相机应用详情界面，点击返回按钮/back键，返回到录音应用详情界面，且点击打开无法进入录音
//            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                intent.data = Uri.fromParts("package", packageName, null)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
            } catch (e: Exception) {
                DebugUtil.e(TAG, "启动$packageName 设置界面失败", e)
            }
            dlg.dismiss()
        }
        val cancelClickListener = DialogInterface.OnClickListener { dlg, _ ->
            dlg.dismiss()
            cancelListener?.invoke()
        }
        builder.setPositiveButton(context.getString(R.string.open), acceptClickListener)
        builder.setNegativeButton(context.getString(R.string.cancel), cancelClickListener)
        val dialog = builder.create()
        dialog.show()
        ViewUtils.updateWindowLayoutParams(dialog.window)
        DebugUtil.d(TAG, "mEnableDialog=$dialog show enable dialog")
        return dialog
    }
}