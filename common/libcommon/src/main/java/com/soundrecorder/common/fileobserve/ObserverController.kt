/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : ObserverController.kt
 * Version Number: 1.0
 * Description   :
 * Author        : chenlipeng
 * Date          : 2020-05-29
 * History       :(ID,  2020-05-29, chenlipeng, Description)
 ************************************************************/

package com.soundrecorder.common.fileobserve

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.common.utils.RecordModeUtil

class ObserverController {
    companion object {
        private const val TAG = "ObserverController"
    }

    private var mMultiFileObserver: MultiFileObserver? = null
    private var mFileEventListener: OnFileEventListener? = null

    fun startFileObserver() {
        CoroutineUtils.ioToMain({
            RecordModeUtil.ensureFoldersExist(BaseApplication.getAppContext())
        }, {
            mMultiFileObserver = MultiFileObserver.getInstance()
                .also {
                    it.addFolders()
                    it.addFileEventListener(mFileEventListener)
                    it.startWatching()
                }
        })
    }

    fun startDeleteFileObserver() {
        mMultiFileObserver?.startDeleteFoldWatching()
    }

    fun stopObserver() {
        stopFileObserver()
    }

    private fun stopFileObserver() {
        if (mMultiFileObserver != null) {
            mMultiFileObserver?.removeFileEventListener(mFileEventListener)
            mMultiFileObserver?.release()
            mMultiFileObserver = null
            mFileEventListener = null
        }
    }

    fun checkHasFolderRemoved(): Boolean {
        return mMultiFileObserver?.checkHasFolderRemoved() ?: false
    }

    fun addFileEventListener(fileEventListener: OnFileEventListener?) {
        mFileEventListener = fileEventListener
    }
}