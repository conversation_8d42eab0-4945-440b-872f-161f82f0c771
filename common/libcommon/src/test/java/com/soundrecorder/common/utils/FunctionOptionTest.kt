/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: NewFeatureOptionTest
 Description:
 Version: 1.0
 Date: 2022/7/28
 Author: W9013333(v-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 --------------------Revision History: ---------------------
 <author> <date> <version> <desc>
 W9013333 2022/7/28 1.0 create
 */
package com.soundrecorder.common.utils

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class FunctionOptionTest {
    private val mockOS12FeatureUtil by lazy { Mockito.mockStatic(OS12FeatureUtil::class.java) }
    private val mockBaseUtil by lazy { Mockito.mockStatic(BaseUtil::class.java) }

    @Test
    fun isSupportPhotoMarkRecommend() {
        mockOS12FeatureUtil.`when`<Boolean> { OS12FeatureUtil.isSuperSoundRecorderEpicEffective() }
            .thenReturn(true)
        FunctionOption.putSupportPhotoMarkRecommend(false)
        mockBaseUtil.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(false)
        Assert.assertFalse(FunctionOption.isSupportPhotoMarkRecommend())
        FunctionOption.putSupportPhotoMarkRecommend(true)
        Assert.assertTrue(FunctionOption.isSupportPhotoMarkRecommend())
        mockBaseUtil.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(true)
        Assert.assertEquals(
            FunctionOption.isSupportPhotoMarkRecommend(),
            PermissionUtils.hasReadAudioPermission()
        )
    }

    @After
    fun clear() {
        mockOS12FeatureUtil.close()
        mockBaseUtil.close()
    }
}