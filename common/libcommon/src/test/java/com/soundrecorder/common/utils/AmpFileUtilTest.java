/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : AmpFileUtilTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/9/27
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/9/27, LI Kun, create
 ************************************************************/

package com.soundrecorder.common.utils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.utils.MakeFileUtils;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.utils.AmpFileUtil;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class AmpFileUtilTest {
    private static final String TEST_STRING = "testString";
    private static final String FILE_PATH = "test" + File.separator + "file" + File.separator + "a.tmp";
    private static final String AMP_FILE_PATH = "Music" + File.separator + "Recordings"
            + File.separator + "Standard Recordings" + File.separator;
    private static final String AMP_FILE_NAME = "test.mp3";
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_null_when_saveAmpFile_with_ampStringNull() {
        File file = AmpFileUtil.saveAmpFile(mContext, null);
        Assert.assertNull(file);
    }

    @Test
    public void should_saveFile_when_saveAmpFile_with_ampStringNotNull() throws IOException {
        File file = AmpFileUtil.saveAmpFile(mContext, TEST_STRING);
        Assert.assertTrue(file.exists());
        Assert.assertTrue(file.isFile());
        String fileText = new String(Files.readAllBytes(Paths.get(file.getPath())));
        Assert.assertEquals(fileText, TEST_STRING);
        file.delete();
    }

    @Test
    public void should_null_when_parseAmpFile_with_ampFilePathNull() {
        String ampFile = AmpFileUtil.parseAmpFile(mContext, null);
        Assert.assertNull(ampFile);
    }

    @Test
    public void should_empty_when_parseAmpFile_with_ampFilePathNotExists() {
        String ampFile = AmpFileUtil.parseAmpFile(mContext, FILE_PATH);
        Assert.assertEquals("", ampFile);
    }

    @Test
    public void should_parseFile_when_parseAmpFile_with_normalFile() {
        File file = AmpFileUtil.saveAmpFile(mContext, TEST_STRING);
        String ampFile = AmpFileUtil.parseAmpFile(mContext, file.getPath());
        Assert.assertEquals(TEST_STRING, ampFile);
        file.delete();
    }

    @Test
    public void should_false_when_ampFileIsExists_with_fileOnlyInDB() {
        Context mockContext = Mockito.mock(Context.class);
        ContentResolver mockContentResolver = Mockito.mock(ContentResolver.class);
        Cursor mockCursor = Mockito.mock(Cursor.class);
        Mockito.when(mockContext.getContentResolver()).thenReturn(mockContentResolver);
        Mockito.when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(),
                (String[]) any(), (String) any())).thenReturn(mockCursor);
        Mockito.when(mockCursor.moveToFirst()).thenReturn(true);
        Mockito.when(mockCursor.getColumnIndex(anyString())).thenReturn(1);
        Mockito.when(mockCursor.getString(1)).thenReturn(AMP_FILE_PATH + AMP_FILE_NAME);
        Mockito.doNothing().when(mockCursor).close();
        boolean fileIsExists = AmpFileUtil.ampFileIsExists(mockContext, AMP_FILE_PATH + AMP_FILE_NAME);
        Assert.assertFalse(fileIsExists);
    }

    @Test
    public void should_true_when_ampFileIsExists_with_fileInDBAndDevice() {
        MakeFileUtils.makeFile(AMP_FILE_PATH, AMP_FILE_NAME);
        Context mockContext = Mockito.mock(Context.class);
        ContentResolver mockContentResolver = Mockito.mock(ContentResolver.class);
        Cursor mockCursor = Mockito.mock(Cursor.class);
        Mockito.when(mockContext.getContentResolver()).thenReturn(mockContentResolver);
        Mockito.when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(),
                (String[]) any(), (String) any())).thenReturn(mockCursor);
        Mockito.when(mockCursor.moveToFirst()).thenReturn(true);
        Mockito.when(mockCursor.getColumnIndex(anyString())).thenReturn(1);
        Mockito.when(mockCursor.getString(1)).thenReturn(AMP_FILE_PATH + AMP_FILE_NAME);
        Mockito.doNothing().when(mockCursor).close();
        boolean fileIsExists = AmpFileUtil.ampFileIsExists(mockContext, AMP_FILE_PATH + AMP_FILE_NAME);
        Assert.assertTrue(fileIsExists);
        MakeFileUtils.delFolder("Music");
    }
}
