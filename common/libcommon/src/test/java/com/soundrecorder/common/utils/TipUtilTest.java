package com.soundrecorder.common.utils;

import static com.soundrecorder.common.utils.TipUtil.TYPE_ROLE;
import static com.soundrecorder.common.utils.TipUtil.TYPE_ROLE_NAME;

import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.view.View;

import androidx.lifecycle.Lifecycle;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowDialog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class TipUtilTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_showDialog_when_onActivityResult_with_requestCodeOne_resultCodeOK() {
        Lifecycle lifecycle = Mockito.mock(Lifecycle.class);
        TipUtil.checkShow(() -> new View(mContext), TYPE_ROLE_NAME, null, lifecycle, false, null, 0, 0);
        Dialog dialog = ShadowDialog.getLatestDialog();
        Assert.assertNull(dialog);
        TipUtil.dismissSelf(TYPE_ROLE_NAME);
        Dialog dialog2 = ShadowDialog.getLatestDialog();
        Assert.assertNull(dialog2);
    }

    @Test
    public void should_return_True_when_hasShowTip() {
        TipUtil.saveShowedTip(TYPE_ROLE);
        Assert.assertTrue(TipUtil.hasShowTip(TYPE_ROLE));
        TipUtil.saveShowedTip(TYPE_ROLE_NAME);
        Assert.assertTrue(TipUtil.hasShowTip(TYPE_ROLE_NAME));
    }

    @After
    public void tearDown() {
        mContext = null;
    }
}
