/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: FollowHandDialogUtilsTest
 Description:
 Version: 1.0
 Date: 2023/03/21
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 22023/03/21 1.0 create
 */
package com.soundrecorder.common.flexible

import android.app.Activity
import android.os.Build
import android.view.View
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.shadows.ShadowCOUIMaxHeightScrollView
import com.soundrecorder.common.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class,
        ShadowCOUIMaxHeightScrollView::class, ShadowCOUIVersionUtil::class]
)
class FollowCOUIAlertDialogTest {

    private var mActivity: Activity? = null

    @Before
    fun setUp() {
        mActivity = Robolectric.buildActivity(Activity::class.java).get()
    }

    @After
    fun tearDown() {
        mActivity = null
    }

    @Test
    fun initShowDialog() {
        mActivity?.let {
            val anchorView = View(it)

            val dialog1 = FollowCOUIAlertDialog(it)
            dialog1.initBuilder()
            dialog1.setAnchorView(null)
            dialog1.showDialog(false)

            val dialog2 = FollowCOUIAlertDialog(it)
            dialog2.initBuilder()
            dialog2.setAnchorView(anchorView)
            dialog2.showDialog(true)
            Assert.assertNotNull(dialog2.getAlertDialog())
            Assert.assertEquals(dialog2.isShowing(), true)
            dialog2.dismiss()
        }
    }
}