/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniAppStaticUtilTest
 * Description:
 * Version: 1.0
 * Date: 2023/8/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/31 1.0 create
 */

package com.soundrecorder.common.buryingpoint

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MiniAppStaticUtilTest {
    private var mockedStatic: MockedStatic<RecorderUserAction>? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mockedStatic = Mockito.mockStatic(RecorderUserAction::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication?.`when`<Context> { BaseApplication.getAppContext() }?.thenReturn(mContext)
    }

    @After
    fun tearDown() {
        mockedStatic?.close()
        mockedBaseApplication?.close()
        mockedBaseApplication = null
        mContext = null
    }

    @Test
    fun should_addNewCommonUserAction_when_addEnterMiniAppEvent() {
        MiniAppStaticUtil.addEnterMiniAppEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, String>(),
                ArgumentMatchers.anyBoolean())
        }, Mockito.times(1))
    }

    @Test
    fun should_addNewCommonUserAction_when_addMiniAppContinueEvent() {
        MiniAppStaticUtil.addMiniAppContinueEvent(MiniAppStaticUtil.MINI_APP_CONTINUE_FROM_DEFAULT)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, String>(),
                ArgumentMatchers.anyBoolean())
        }, Mockito.times(1))
    }
}