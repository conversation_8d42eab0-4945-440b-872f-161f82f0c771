/*********************************************************************
 * * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ZoomWindowUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/9/30
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.app.ActivityOptions
import android.app.OplusActivityManager
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.view.Display
import com.oplus.zoomwindow.OplusZoomWindowManager
import com.oplus.compat.app.ActivityManagerNative

object ZoomWindowUtil {

    private const val TAG = "ZoomWindowUtil"
    private const val KEY_SPLASH_SCREEN_STYLE = "android.activity.splashScreenStyle"
    private const val SPLASH_SCREEN_STYLE_ICON = 1
    private const val RESULT_CODE_ZOOM_WINDOW_FAILED = -91
    private const val ERROR_CODE_ZOOM_WINDOW_FAILED = -1
    private const val ERROR_CODE_ACTIVITY_NOT_FOUND = -3
    private const val ERROR_CODE_ACTIVITY_NOT_EXPORT = -4

    @JvmStatic
    fun startActivityAsZoomWindow(
        context: Context,
        intent: Intent,
        openFailedCallback: ((Int) -> Unit) = {}
    ) {
        val bundle = ActivityOptions.makeBasic().setLaunchDisplayId(Display.DEFAULT_DISPLAY).toBundle()
        bundle.putInt(OplusZoomWindowManager.EXTRA_WINDOW_MODE, OplusZoomWindowManager.WINDOWING_MODE_ZOOM)
        bundle.putInt(KEY_SPLASH_SCREEN_STYLE, SPLASH_SCREEN_STYLE_ICON)
        try {
            //这个接口需要申请授权码
            val userId = getCurrentUser()
            val result = OplusZoomWindowManager.getInstance().startZoomWindow(intent, bundle, userId, context.packageName)

            if (result == RESULT_CODE_ZOOM_WINDOW_FAILED) {
                openFailedCallback.invoke(ERROR_CODE_ZOOM_WINDOW_FAILED)
            }
        } catch (e: ActivityNotFoundException) {
            openFailedCallback.invoke(ERROR_CODE_ACTIVITY_NOT_FOUND)
            DebugUtil.e(TAG, "startActivityAsZoomWindow ActivityNotFoundException")
        } catch (e: SecurityException) {
            val message = e.message ?: ""
            val errorPattern1 = "Permission Denial: starting Intent"
            val errorPattern2 = "not exported from"
            if (message.startsWith(errorPattern1) && message.contains(errorPattern2)) {
                openFailedCallback.invoke(ERROR_CODE_ACTIVITY_NOT_EXPORT)
            }
            DebugUtil.e(TAG, "startActivityAsZoomWindow SecurityException ${e.message}")
        } catch (e: java.lang.Exception) {
            DebugUtil.e(TAG, "startActivityAsZoomWindow exception: " + e.message + " ${e.javaClass}")
        }
    }

    @JvmStatic
    fun getCurrentUser(): Int {
        kotlin.runCatching {
            return if (OS12FeatureUtil.isColorOS15OrLater()) {
                val oplusActivityManager = OplusActivityManager()
                oplusActivityManager.currentUser
            } else {
                ActivityManagerNative.getCurrentUser()
            }
        }.onFailure {
            DebugUtil.e("AddonApi", "getCurrentUser ${it.message}")
        }
        return 0
    }
}