/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BracketSpaceProviderAgentTest
 * Description:
 * Version: 1.0
 * Date: 2022/9/25
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/9/25 1.0 create
 */

package com.soundrecorder.base.splitwindow.bracketspace

import android.content.ContentProviderClient
import android.content.ContentResolver
import android.content.Context
import android.content.res.AssetManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.FeatureOption
import java.io.InputStream
import java.io.OutputStream
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.Mockito.any
import org.mockito.Mockito.anyString
import org.mockito.Mockito.mock
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class BracketSpaceProviderAgentTest {
    private val mBracketSpacePkgName = "com.oplus.bracketspace"
    private var mMockedFeatureOption: MockedStatic<FeatureOption>? = null
    private var mMockedAppUtil: MockedStatic<AppUtil>? = null

    @Before
    fun setup() {
        mMockedFeatureOption = Mockito.mockStatic(FeatureOption::class.java)
        mMockedAppUtil = Mockito.mockStatic(AppUtil::class.java)
        mMockedFeatureOption?.`when`<Boolean> { FeatureOption.getIsFoldFeature() }?.thenReturn(true)
        mMockedAppUtil?.`when`<Boolean> { AppUtil.isAppInstalled(mBracketSpacePkgName) }?.thenReturn(true)
    }

    @After
    fun release() {
        mMockedFeatureOption?.close()
        mMockedFeatureOption = null
        mMockedAppUtil?.close()
        mMockedAppUtil = null
    }

    @Test
    fun check_checkCanCallBracketSpaceProvider() {
        mMockedFeatureOption?.`when`<Boolean> { FeatureOption.getIsFoldFeature() }?.thenReturn(false, true, true)
        Assert.assertFalse(BracketSpaceProviderAgent.checkCanCallBracketSpaceProvider())

        mMockedAppUtil?.`when`<Boolean> { AppUtil.isAppInstalled(mBracketSpacePkgName) }?.thenReturn(false, true)
        Assert.assertFalse(BracketSpaceProviderAgent.checkCanCallBracketSpaceProvider())
        Assert.assertTrue(BracketSpaceProviderAgent.checkCanCallBracketSpaceProvider())
    }

    @Test
    fun check_updateAppLaunchTime() {
        val context = mock(Context::class.java)
        val mockContentResolver = mock(ContentResolver::class.java)
        val mockContentProviderClient = mock(ContentProviderClient::class.java)
        `when`(mockContentProviderClient.call(anyString(), anyString(), any(Bundle::class.java))).thenReturn(null as? Bundle?,
            bundleOf("call_result" to 1))
        `when`(mockContentResolver.acquireUnstableContentProviderClient(any(Uri::class.java)))
            .thenReturn(null as? ContentProviderClient?, mockContentProviderClient)
        `when`(context.contentResolver).thenReturn(mockContentResolver)

        Assert.assertFalse(BracketSpaceProviderAgent.updateAppLaunchTime(context))
//        Assert.assertTrue(BracketSpaceProviderAgent.updateAppLaunchTime(context))
    }

    @Test
    fun check_insertBracketSpaceData() {
        val context = mock(Context::class.java)
        val mockContentResolver = mock(ContentResolver::class.java)
        val mockContentProviderClient = mock(ContentProviderClient::class.java)
        `when`(mockContentProviderClient.call(anyString(), anyString(), any(Bundle::class.java)))
            .thenReturn(null as? Bundle?, bundleOf("call_result" to 1))
        `when`(mockContentResolver.acquireUnstableContentProviderClient(any(Uri::class.java)))
            .thenReturn(null as? ContentProviderClient?, mockContentProviderClient)
        `when`(context.contentResolver).thenReturn(mockContentResolver)

        Assert.assertFalse(BracketSpaceProviderAgent.insertBracketSpaceData(context, MessageEntryBean("01")))
//        Assert.assertTrue(BracketSpaceProviderAgent.insertBracketSpaceData(context, MessageEntryBean("01")))
    }

    @Test
    fun check_writePictureToBracketDir() {
        val context = mock(Context::class.java)
        val mockAsset = mock(AssetManager::class.java)
        val mockContentResolver = mock(ContentResolver::class.java)
        `when`(context.assets).thenReturn(mockAsset)
        `when`(mockAsset.open(anyString())).thenReturn(mock(InputStream::class.java))
        `when`(context.contentResolver).thenReturn(mockContentResolver)
        `when`(mockContentResolver.openOutputStream(any(Uri::class.java))).thenReturn(null as? OutputStream?)
        Assert.assertFalse(BracketSpaceProviderAgent.writePictureToBracketDir(context, "record.png", "record.png"))

        val mockInputStream = mock(InputStream::class.java)
        val mockOutStream = mock(OutputStream::class.java)
        `when`(mockInputStream.read(any(ByteArray::class.java))).thenReturn(1, -1)
        `when`(mockAsset.open(anyString())).thenReturn(mockInputStream)
        `when`(mockContentResolver.openOutputStream(any(Uri::class.java))).thenReturn(mockOutStream)
        Assert.assertTrue(BracketSpaceProviderAgent.writePictureToBracketDir(context, "record.png", "record.png"))
    }
}