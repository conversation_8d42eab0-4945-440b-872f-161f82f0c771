/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  SellModeInterface.kt
 * * Description : SellModeInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.sellmode

import android.content.Context
import androidx.lifecycle.LifecycleOwner

interface SellModeInterface {
    fun checkAndStartSellModeService(context: Context)
    fun createSellModeScreenStateListener(lifecycleOwner: LifecycleOwner, screenChangedCheck: (() -> Boolean)?)
}