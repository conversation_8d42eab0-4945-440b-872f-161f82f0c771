/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  CloudKitInterface.kt
 * * Description : CloudKitInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.cloudkit

import android.app.Activity
import android.content.Context
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudSyncStatusDialog
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudUpgradeHelper

interface CloudKitInterface {
    fun doMediaCompare(trigCloudSyncRightNow: Boolean, syncType: Int = -1)
    fun doStopMediaCompare(stop: Boolean)
    fun getCloudGlobalState(showErrorTip: Boolean, callback: ICloudGlobalStateCallBack?)
    fun initCloudGlobalState(callback: ICloudGlobalStateCallBack?)
    fun registerGlobalStateCallback(callback: ICloudGlobalStateCallBack)
    fun unregisterGlobalStateCallback(callback: ICloudGlobalStateCallBack)
    fun showGlobalDisableDialog(activity: Activity?, state: String?, buttonListener: (() -> Unit)?): AlertDialog?
    fun showGlobalLoadingDialog(activity: Activity?): AlertDialog?
    fun setCloudGrantedStatus(context: Context)
    fun clearCloudGrantedStatus()
    fun isStatementCloudGranted(context: Context): Boolean
    fun hasCloudRequiredPermissions(): Boolean
    fun isNetWorkGranted(context: Context): Boolean
    fun setIgnoreHighTemperature(ignore: Boolean)
    fun isSupportCloudArea(): Boolean
    fun queryCloudSwitchState(checkLogin: Boolean = true): Int
    fun registerPushIfNeed(context: Context)
    fun stopSyncForClearAnchor(context: Context)
    fun trigMediaDBSync(context: Context, syncType: Int)
    fun trigCloudSync(context: Context, syncType: Int)
    fun trigBackupNow(context: Context)
    fun stopSyncForLoginOut(context: Context, deleteData: Boolean)
    fun release()
    fun isSupportSwitch(): Boolean
    fun setSyncSwitch(state: Int, report: Boolean = true): Boolean
    fun scheduleSyncRunnable(context: Context, runnable: Runnable, delayTime: Long): Boolean
    fun checkLoginAndVerify(activity: Activity, callback: VerifyCallBack)
    fun checkAccountIsVerified(): Boolean
    fun getCloudSettings(dataStr: MutableLiveData<String>)
    fun clearLastUserData(context: Context)
    fun isCloudOn(): Boolean
    fun newCloudStatusDialog(mContext: Context, owner: LifecycleOwner?): ICloudSyncStatusDialog
    fun newCloudUpgradeHelper(): ICloudUpgradeHelper
    fun requestSignLogin(context: Context)
    fun launchCloudApp(context: Context)
    fun isMediaComparing(): Boolean
}


interface ICloudGlobalStateCallBack {
    fun onSuccess(changed: Boolean, support: Boolean, state: String?) {}

    fun onFailure() {}
}


interface VerifyCallBack {
    fun onSuccess()
    fun onFail()
}

// 对外暴露的syncType
const val SYNC_TYPE_BACKUP = 0X1
const val SYNC_TYPE_RECOVERY_MANUAL = 0X2
const val SYNC_TYPE_RECOVERY_START_APP = 0X3
const val SYNC_TYPE_DEFAULT = -1