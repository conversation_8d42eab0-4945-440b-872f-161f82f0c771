/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AppCardInterface.kt
 * * Description : AppCardInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.os.Bundle

// 旧卡编号需要与服务器上的配置对应，否则找不到卡片。
const val CARD_TYPE_FOR_DRAGON_FLY = 777770016
const val CARD_TYPE_FOR_SMALL_CARD = 222220090
const val CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD = 222220121

//速览卡上推荐的服务卡片旧卡编号，
// const val CARD_TYPE_FOR_RECOMMEND_SMALL_CARD = 255461  // 测试环境
const val CARD_TYPE_FOR_RECOMMEND_SMALL_CARD = 201565  // 正式环境

interface AppCardInterface {
    fun addWidgetCodes(widgetCode: String)
    fun addWidgetCodesOnResume(widgetCode: String)
    fun removeWidgetCodeOnPause(widgetCode: String)
    fun callFromDragonFlyCard(method: String, widgetCode: String): Bundle?
    fun callFromSmallCard(method: String, widgetCode: String, isRecommendCard: Boolean = false): Bundle?
}