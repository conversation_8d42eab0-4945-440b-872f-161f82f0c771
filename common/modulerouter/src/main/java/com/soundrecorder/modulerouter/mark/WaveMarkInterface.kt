/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  WaveMarkInterface.kt
 * * Description : WaveMarkInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.mark

import android.net.Uri

interface WaveMarkInterface {

    fun decodeAmplitudeByUri(uri: Uri?): String
    fun <T, R> newPictureMarkDelegate(
        ownerProvider: IPictureMarkLifeOwnerProvider,
        isActivityRecreate: Boolean,
        listener: IIPictureMarkListener<T, R>?
    ): IPictureMarkDelegate<T>

    fun getMergeMarkList(path: String, playUri: Uri, isRecycle: Boolean): List<Any>
}