/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  EditRecordInterface.kt
 * * Description : EditRecordInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.content.Context
import android.content.Intent

interface EditRecordInterface {
    fun createEditRecordIntent(context: Context): Intent?
    fun isEditRecordActivity(context: Context): Boolean
    fun getEditRecordActivityClass(): Class<*>?
}